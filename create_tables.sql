-- 创建数据库（如果不存在）
-- CREATE DATABASE flink_alerts;

-- 使用数据库
-- \c flink_alerts;

-- 创建告警事件表
CREATE TABLE IF NOT EXISTS alert_events (
    user_id VARCHAR(100) NOT NULL,
    alert_type VARCHAR(50) NOT NULL,
    message TEXT NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_alert_events_user_id ON alert_events(user_id);
CREATE INDEX IF NOT EXISTS idx_alert_events_alert_type ON alert_events(alert_type);
CREATE INDEX IF NOT EXISTS idx_alert_events_timestamp ON alert_events(timestamp);
CREATE INDEX IF NOT EXISTS idx_alert_events_created_at ON alert_events(created_at);

-- 添加注释
COMMENT ON TABLE alert_events IS '告警事件表';
COMMENT ON COLUMN alert_events.user_id IS '用户ID';
COMMENT ON COLUMN alert_events.alert_type IS '告警类型：NETWORK_UNAVAILABLE-网络不通, POOR_NETWORK_QUALITY-网络质量差';
COMMENT ON COLUMN alert_events.message IS '告警消息';
COMMENT ON COLUMN alert_events.timestamp IS '事件时间戳';
COMMENT ON COLUMN alert_events.created_at IS '记录创建时间'; 