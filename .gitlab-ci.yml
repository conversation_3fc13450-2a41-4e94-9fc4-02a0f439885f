#$DOCKER_NEXUS_USER nexus docker repo username
#$DOCKER_NEXUS_PASSWORD nexus docke rrepo user password
#$DOCKER_NEXUS_URL  **************:5000

stages:
  - build
  - package
#  - deploy
#  - deploy_3
#  - deploy_88
  - deliver
  - release

variables:
  COMPONENT_NAME: flink-demo

#发布版本
build:
  before_script:
    - export
    - OBJECT_VERSION=${CI_COMMIT_REF_NAME##*/}
    - BuildNo="$(date +%Y%m%d)_${CI_PIPELINE_ID}"
    - echo "发布版本：${OBJECT_VERSION}"
    - echo "COMMIT REF:${CI_COMMIT_REF_NAME}"
  stage: build
  tags:
    - maven_runner_wh
  script:

    # 如果是merge request 构建 release版本，否则构建snapshot版本
    - mvn clean install -DskipTests=true -U -s ./.ci.setting.xml
    - mvn clean package -DskipTests=true -U -s ./.ci.setting.xml
    - mv ./target ./target_real
    - mkdir -p ./target
    - ls ./target_real
    - mv ./target_real/flink_demo*.jar ./target/flink-demo.jar
    - 'echo -e "Version:${OBJECT_VERSION}\nBuild:${BuildNo}\nName:${COMPONENT_NAME}" > ./target/version'
    - echo -e "Version:${OBJECT_VERSION}\nBuild:${BuildNo}\nName:${COMPONENT_NAME}" > ./target/version
    - cat ./target/version
    - echo "Compile  complete."
    - echo "OBJECT_VERSION=${OBJECT_VERSION}" >> variables.env
    - echo "OBJECT_BUILD_NO=${BuildNo}" >> variables.env
  only:
    -  /^release\/.*$/
    - merge_requests
  artifacts:
    name: sds_admin
    expire_in: 1 day
    reports:
      dotenv: variables.env
    paths:
      - ./target


package:
  tags:
    - shell_wh
  stage: package
  script:
    - export
    - ls -l
#    - cp -r ./deploy/host/config ./target/
#    - cp -r ./deploy/host/install/ ./target/
  needs:
    - job: build
      artifacts: true
  artifacts:
    name: ${COMPONENT_NAME}_${OBJECT_VERSION}_${OBJECT_BUILD_NO}
    expire_in: 1 day
    public: true
    paths:
      - target
  only:
    - /^release\/.*$/
    - merge_requests
