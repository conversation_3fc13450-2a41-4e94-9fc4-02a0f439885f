# DynamicCEPJob系统架构图

```mermaid
graph TB
    subgraph "管理层"
        DMP["DMP<br/>规则管理平台<br/>• Web界面<br/>• 规则配置<br/>• 任务管理"]
    end
    
    subgraph "消息通道"
        XMSG["X-MSG<br/>消息队列<br/>• 任务下发<br/>• 状态同步<br/>• 命令传递"]
    end
    
    subgraph "Flink集群"
        JM["Flink JobManager<br/>• 作业调度<br/>• 资源管理<br/>• 检查点协调"]
        CLIENT["XMSG Client<br/>• 消息拉取<br/>• 命令执行<br/>• 状态上报"]
        TM1["Flink TaskManager 1<br/>• CEP处理<br/>• 数据计算<br/>• 状态管理"]
        TM2["Flink TaskManager 2<br/>• CEP处理<br/>• 数据计算<br/>• 状态管理"]
        TM3["Flink TaskManager N<br/>• CEP处理<br/>• 数据计算<br/>• 状态管理"]
    end
    
    subgraph "数据输入层"
        INPUT1["NATS<br/>当前实现"]
        INPUT2["Kafka<br/>待扩展支持"]
        INPUT3["其他消息队列<br/>待扩展支持"]
    end
    
    subgraph "数据输出层"
        OUTPUT1["PostgreSQL<br/>当前实现"]
        OUTPUT2["Elasticsearch<br/>待扩展支持"]
        OUTPUT3["其他存储<br/>待扩展支持"]
    end
    
    subgraph "配置存储"
        DB["PostgreSQL<br/>• 规则配置<br/>• 告警记录<br/>• 系统元数据"]
    end
    
    %% 连接关系
    DMP -->|规则配置| DB
    DMP -->|任务管理消息| XMSG
    
    XMSG -->|拉取任务| CLIENT
    CLIENT -.->|部署在同一节点| JM
    
    JM -->|调度任务| TM1
    JM -->|调度任务| TM2
    JM -->|调度任务| TM3
    
    INPUT1 -->|日志数据| TM1
    INPUT2 -.->|日志数据| TM1
    INPUT3 -.->|日志数据| TM1
    
    INPUT1 -->|日志数据| TM2
    INPUT2 -.->|日志数据| TM2
    INPUT3 -.->|日志数据| TM2
    
    INPUT1 -->|日志数据| TM3
    INPUT2 -.->|日志数据| TM3
    INPUT3 -.->|日志数据| TM3
    
    TM1 -->|规则配置查询| DB
    TM2 -->|规则配置查询| DB
    TM3 -->|规则配置查询| DB
    
    TM1 -->|告警结果| OUTPUT1
    TM1 -.->|告警结果| OUTPUT2
    TM1 -.->|告警结果| OUTPUT3
    
    TM2 -->|告警结果| OUTPUT1
    TM2 -.->|告警结果| OUTPUT2
    TM2 -.->|告警结果| OUTPUT3
    
    TM3 -->|告警结果| OUTPUT1
    TM3 -.->|告警结果| OUTPUT2
    TM3 -.->|告警结果| OUTPUT3
    
    %% 样式
    classDef current fill:#e1f5fe
    classDef future fill:#fff3e0
    classDef core fill:#f3e5f5
    
    class INPUT1,OUTPUT1 current,INPUT2
    class INPUT3,OUTPUT2,OUTPUT3 future
    class JM,TM1,TM2,TM3,CLIENT,XMSG,DMP core
```

## 图表说明

- **蓝色组件**：当前已实现的功能
- **橙色组件**：未来可扩展的功能  
- **紫色组件**：核心处理组件
- **实线**：当前数据流
- **虚线**：扩展数据流或依赖关系 