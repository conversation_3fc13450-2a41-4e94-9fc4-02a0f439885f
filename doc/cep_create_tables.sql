-- CEP项目数据库表创建脚本
-- 包含规则配置表和告警事件表
DROP TABLE zj_cep_rules;
-- ==============================================
-- 1. 创建CEP规则配置表
-- ==============================================
CREATE TABLE IF NOT EXISTS zj_cep_rules (
    id VARCHAR(255) PRIMARY KEY,                    -- 规则配置ID，唯一标识
    group_key VARCHAR(255),                         -- 用于keyBy的字段名，如user_id、client_ip等
    rules JSONB NOT NULL,                           -- JSON格式的规则配置，包含规则组和子规则信息
    within JSONB,                                   -- JSON格式的CEP时间窗口配置，用于Pattern的within()方法
    status INTEGER DEFAULT 1,                      -- 规则状态：1-启用，0-禁用
    add_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,   -- 规则添加时间
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 规则更新时间
);

-- 添加zj_cep_rules表的索引
CREATE INDEX IF NOT EXISTS idx_zj_cep_rules_status ON zj_cep_rules(status);
CREATE INDEX IF NOT EXISTS idx_zj_cep_rules_add_time ON zj_cep_rules(add_time);
CREATE INDEX IF NOT EXISTS idx_zj_cep_rules_update_time ON zj_cep_rules(update_time);
CREATE INDEX IF NOT EXISTS idx_zj_cep_rules_group_key ON zj_cep_rules(group_key);

-- 添加zj_cep_rules表注释
COMMENT ON TABLE zj_cep_rules IS 'CEP规则配置表，存储动态CEP规则的完整配置信息';
COMMENT ON COLUMN zj_cep_rules.id IS '规则配置ID，主键，唯一标识一个CEP规则配置';
COMMENT ON COLUMN zj_cep_rules.group_key IS '分组键字段名，用于keyBy操作，如user_id、client_ip、session_id等';
COMMENT ON COLUMN zj_cep_rules.rules IS '规则配置，JSON格式，包含规则组、数据源、子规则、条件表达式等完整信息';
COMMENT ON COLUMN zj_cep_rules.within IS 'CEP时间窗口配置，JSON格式，用于Pattern的within()方法限制匹配时间范围';
COMMENT ON COLUMN zj_cep_rules.status IS '规则状态：1-启用（运行中），0-禁用（停止）';
COMMENT ON COLUMN zj_cep_rules.add_time IS '规则添加时间，记录规则首次创建的时间';
COMMENT ON COLUMN zj_cep_rules.update_time IS '规则更新时间，记录规则最后修改的时间';

-- ==============================================
-- 2. 创建CEP告警事件表
-- ==============================================
CREATE TABLE IF NOT EXISTS zj_cep_alert_events (
    id BIGSERIAL PRIMARY KEY,                          -- 自增主键ID
    group_key VARCHAR(255),                         -- 分组键值，对应触发告警的具体分组
    alert_type VARCHAR(100) NOT NULL,               -- 告警类型，如COMBINED_RULES_MATCHED
    message TEXT,                                   -- 告警消息，详细描述告警内容
    timestamp TIMESTAMP NOT NULL,                   -- 告警发生时间
    rule_id VARCHAR(255) NOT NULL,                  -- 触发告警的规则ID，关联zj_cep_rules表
    alert_content JSONB,                            -- 告警详细内容，JSON格式的完整匹配Pattern信息
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 记录创建时间
);

-- 添加zj_cep_alert_events表的索引
CREATE INDEX IF NOT EXISTS idx_zj_cep_alert_events_rule_id ON zj_cep_alert_events(rule_id);
CREATE INDEX IF NOT EXISTS idx_zj_cep_alert_events_timestamp ON zj_cep_alert_events(timestamp);
CREATE INDEX IF NOT EXISTS idx_zj_cep_alert_events_group_key ON zj_cep_alert_events(group_key);
CREATE INDEX IF NOT EXISTS idx_zj_cep_alert_events_alert_type ON zj_cep_alert_events(alert_type);
CREATE INDEX IF NOT EXISTS idx_zj_cep_alert_events_created_time ON zj_cep_alert_events(created_time);

-- 添加zj_cep_alert_events表注释
COMMENT ON TABLE zj_cep_alert_events IS 'CEP告警事件表，存储CEP规则匹配产生的告警信息';
COMMENT ON COLUMN zj_cep_alert_events.id IS '自增主键ID，唯一标识每个告警事件';
COMMENT ON COLUMN zj_cep_alert_events.group_key IS '分组键值，触发告警的具体分组值，如具体的用户ID或IP地址';
COMMENT ON COLUMN zj_cep_alert_events.alert_type IS '告警类型，标识告警的类别，如COMBINED_RULES_MATCHED、SECURITY_ALERT等';
COMMENT ON COLUMN zj_cep_alert_events.message IS '告警消息，人类可读的告警描述信息';
COMMENT ON COLUMN zj_cep_alert_events.timestamp IS '告警发生时间，CEP模式匹配成功的时间';
COMMENT ON COLUMN zj_cep_alert_events.rule_id IS '触发告警的规则ID，外键关联zj_cep_rules表的id字段';
COMMENT ON COLUMN zj_cep_alert_events.alert_content IS '告警详细内容，JSON格式，包含完整的匹配Pattern、匹配事件等信息';
COMMENT ON COLUMN zj_cep_alert_events.created_time IS '记录创建时间，告警事件入库的时间';

-- ==============================================
-- 3. 创建外键关联
-- ==============================================
-- 添加外键约束，确保告警事件关联的规则ID存在
ALTER TABLE zj_cep_alert_events 
ADD CONSTRAINT fk_alert_events_rule_id 
FOREIGN KEY (rule_id) REFERENCES zj_cep_rules(id) ON DELETE CASCADE;

-- ==============================================
-- 4. 初始化示例数据
-- ==============================================

-- 示例1：用户认证失败监控规则
INSERT INTO zj_cep_rules (id, group_key, rules, within, status) 
VALUES (
    'auth_failure_monitor',
    'user_id',
    '[
      {
        "id": "auth_group",
        "source": ["auth_log"],
        "rules": [
          {
            "id": "failed_attempts",
            "times": [3, 999],
            "greedy": false,
            "optional": false,
            "one_or_more": false,
            "condition": [
              {
                "id": "1",
                "type": "match",
                "match_key": "status",
                "match_type": "=",
                "match_val": "FAILED"
              }
            ],
            "condition_expression": "1"
          },
          {
            "id": "success_after_failures",
            "times": [1, 1],
            "greedy": false,
            "optional": false,
            "one_or_more": false,
            "condition": [
              {
                "id": "1",
                "type": "match",
                "match_key": "status",
                "match_type": "=",
                "match_val": "SUCCESS"
              }
            ],
            "condition_expression": "1"
          }
        ]
      }
    ]'::jsonb,
    '{"time": 60, "unit": "s"}'::jsonb,
    1
) ON CONFLICT (id) DO NOTHING;

-- 示例2：高频API调用监控规则
INSERT INTO zj_cep_rules (id, group_key, rules, within, status) 
VALUES (
    'api_frequency_monitor',
    'client_ip',
    '[
      {
        "id": "api_group",
        "source": ["api_log"],
        "rules": [
          {
            "id": "high_frequency_calls",
            "times": [10, 999],
            "greedy": false,
            "optional": false,
            "one_or_more": false,
            "condition": [
              {
                "id": "1",
                "type": "match",
                "match_key": "endpoint",
                "match_type": "contains",
                "match_val": "/api/"
              },
              {
                "id": "2",
                "type": "match",
                "match_key": "response_time",
                "match_type": ">",
                "match_val": "1000"
              }
            ],
            "condition_expression": "1 AND 2"
          }
        ]
      }
    ]'::jsonb,
    '{"time": 300, "unit": "s"}'::jsonb,
    1
) ON CONFLICT (id) DO NOTHING;

-- 示例3：多数据源安全事件关联分析
INSERT INTO zj_cep_rules (id, group_key, rules, within, status) 
VALUES (
    'security_correlation_analysis',
    'session_id',
    '[
      {
        "id": "security_group",
        "source": ["tunnel_log", "auth_log", "business_log"],
        "rules": [
          {
            "id": "suspicious_tunnel",
            "times": [1, 1],
            "greedy": false,
            "optional": false,
            "one_or_more": false,
            "condition": [
              {
                "id": "1",
                "type": "match",
                "match_key": "event_type",
                "match_type": "=",
                "match_val": "TUNNEL_ESTABLISHED"
              },
              {
                "id": "2",
                "type": "match",
                "match_key": "source_ip",
                "match_type": "regex",
                "match_val": "^(10\\.|192\\.168\\.|172\\.(1[6-9]|2[0-9]|3[01])\\.).*"
              }
            ],
            "condition_expression": "1 AND NOT 2"
          },
          {
            "id": "privilege_escalation",
            "times": [1, 5],
            "greedy": false,
            "optional": true,
            "one_or_more": false,
            "condition": [
              {
                "id": "1",
                "type": "match",
                "match_key": "action",
                "match_type": "contains",
                "match_val": "privilege"
              },
              {
                "id": "2",
                "type": "match",
                "match_key": "result",
                "match_type": "=",
                "match_val": "success"
              }
            ],
            "condition_expression": "1 AND 2"
          }
        ]
      }
    ]'::jsonb,
    '{"time": 600, "unit": "s"}'::jsonb,
    1
) ON CONFLICT (id) DO NOTHING;

-- 示例4：简单错误检测规则
INSERT INTO zj_cep_rules (id, group_key, rules, within, status) 
VALUES (
    'simple_error_detection',
    'application_name',
    '[
      {
        "id": "error_group",
        "source": [],
        "rules": [
          {
            "id": "critical_error",
            "times": [1, 1],
            "greedy": false,
            "optional": false,
            "one_or_more": false,
            "condition": [
              {
                "id": "1",
                "type": "match",
                "match_key": "level",
                "match_type": "=",
                "match_val": "ERROR"
              },
              {
                "id": "2",
                "type": "match",
                "match_key": "severity",
                "match_type": ">=",
                "match_val": "8"
              }
            ],
            "condition_expression": "1 AND 2"
          }
        ]
      }
    ]'::jsonb,
    '{"time": 30, "unit": "s"}'::jsonb,
    1
) ON CONFLICT (id) DO NOTHING;

-- ==============================================
-- 5. 创建触发器更新update_time
-- ==============================================
-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为zj_cep_rules表添加更新时间触发器
DROP TRIGGER IF EXISTS update_zj_cep_rules_modtime ON zj_cep_rules;
CREATE TRIGGER update_zj_cep_rules_modtime 
    BEFORE UPDATE ON zj_cep_rules 
    FOR EACH ROW 
    EXECUTE FUNCTION update_modified_column();

-- ==============================================
-- 6. 显示建表完成信息
-- ==============================================
SELECT 'CEP数据库表创建完成！' as message;
SELECT 'zj_cep_rules表: 用于存储CEP规则配置，支持复杂条件表达式和时间窗口机制' as info;
SELECT 'zj_cep_alert_events表: 用于存储告警事件，包含完整的匹配信息' as info;
SELECT '已插入4个示例规则配置，展示不同的使用场景' as info; 