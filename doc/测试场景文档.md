# DynamicCEPJob测试场景文档

## 测试场景概述

本文档定义了DynamicCEPJob系统的测试场景，包含2种类型的日志和2个复杂的CEP匹配规则，用于验证系统的复杂事件处理能力。

## 日志类型定义

### 1. 认证日志 (auth_log)

认证日志记录用户的登录认证行为。

#### 字段定义
- **__log_type__**: "auth_log" (固定值)
- **__timestamp__**: 事件时间戳
- **user_id**: 用户ID (String)
- **res**: 认证结果枚举值

#### res字段枚举值
- **AUTH_FAIL**: 认证失败
- **AUTH_SUCCESS**: 认证成功

#### JSON消息示例

**认证成功示例:**
```json
{
  "__log_type__": "auth_log",
  "__timestamp__": "2024-01-15T10:30:45.123Z",
  "user_id": "user001",
  "res": "AUTH_SUCCESS"
}
```

**认证失败示例:**
```json
{
  "__log_type__": "auth_log",
  "__timestamp__": "2024-01-15T10:31:12.456Z",
  "user_id": "user002",
  "res": "AUTH_FAIL"
}
```

### 2. 隧道日志 (tunnel_log)

隧道日志记录用户的连接隧道操作行为。

#### 字段定义
- **__log_type__**: "tunnel_log" (固定值)
- **__timestamp__**: 事件时间戳
- **user_id**: 用户ID (String)
- **res**: 隧道操作结果枚举值

#### res字段枚举值
- **OPEN**: 打开隧道连接
- **CLOSE**: 关闭隧道连接

#### JSON消息示例

**打开隧道示例:**
```json
{
  "__log_type__": "tunnel_log",
  "__timestamp__": "2024-01-15T10:32:30.789Z",
  "user_id": "user001",
  "res": "OPEN"
}
```

**关闭隧道示例:**
```json
{
  "__log_type__": "tunnel_log",
  "__timestamp__": "2024-01-15T10:35:45.123Z",
  "user_id": "user001",
  "res": "CLOSE"
}
```

## CEP匹配规则定义

### 规则1: 反复隧道连接检测

#### 业务场景
检测用户在短时间内反复进行隧道的打开和关闭操作，这可能表示：
- 网络不稳定导致的频繁重连
- 恶意用户的异常行为
- 系统配置问题

#### 规则配置

```sql
INSERT INTO zj_cep_rules (id, group_key, rules, within, status)
VALUES (
           'tunnel_repeated_operations',
           'user_id',
           '[
             {
               "id": "tunnel_operation_group",
               "source": ["tunnel_log"],
               "rules": [
                 {
                   "id": "tunnel_open",
                   "times": [1, 1],
                   "greedy": false,
                   "optional": false,
                   "one_or_more": false,
                   "follow": true,
                   "not": false,
                   "condition": [
                     {
                       "id": "1",
                       "type": "match",
                       "match_key": "res",
                       "match_type": "=",
                       "match_val": "OPEN"
                     }
                   ],
                   "condition_expression": "1"
                 },
                 {
                   "id": "tunnel_close",
                   "times": [1, 1],
                   "greedy": false,
                   "optional": false,
                   "one_or_more": false,
                   "follow": true,
                   "not": false,
                   "condition": [
                     {
                       "id": "1",
                       "type": "match",
                       "match_key": "res",
                       "match_type": "=",
                       "match_val": "CLOSE"
                     }
                   ],
                   "condition_expression": "1"
                 },
                 {
                   "id": "tunnel_open_again",
                   "times": [1],
                   "greedy": false,
                   "optional": false,
                   "one_or_more": false,
                   "follow": true,
                   "not": false,
                   "condition": [
                     {
                       "id": "1",
                       "type": "match",
                       "match_key": "res",
                       "match_type": "=",
                       "match_val": "OPEN"
                     }
                   ],
                   "condition_expression": "1"
                 }
               ]
             }
           ]'::jsonb,
           '{"time": 60, "unit": "s"}'::jsonb,
           1
       );
```

#### 匹配逻辑说明
1. **第一步**: 检测到用户打开隧道 (OPEN)
2. **第二步**: 后续检测到用户关闭隧道 (CLOSE)
3. **第三步**: 再次检测到用户打开隧道 (OPEN)，可以多次
4. **时间窗口**: 整个序列必须在60秒内完成

#### 匹配示例数据序列
```
用户user001在1分钟内:
10:30:00 - OPEN
10:30:15 - CLOSE  
10:30:30 - OPEN   ← 触发告警
10:30:45 - CLOSE
10:30:50 - OPEN   ← 继续匹配
```

### 规则2: 认证成功但连接失败检测

#### 业务场景
检测用户认证成功后未能建立隧道连接的情况，这可能表示：
- 系统授权问题
- 网络连接问题
- 隧道服务异常
- 用户权限不足

#### 规则配置

```sql
INSERT INTO zj_cep_rules (id, group_key, rules, within, status)
VALUES (
           'auth_success_tunnel_fail',
           'user_id',
           '[
             {
               "id": "auth_success_group",
               "source": ["auth_log"],
               "rules": [
                 {
                   "id": "auth_success_event",
                   "times": [1, 1],
                   "greedy": false,
                   "optional": false,
                   "one_or_more": false,
                   "follow": true,
                   "not": false,
                   "condition": [
                     {
                       "id": "1",
                       "type": "match",
                       "match_key": "res",
                       "match_type": "=",
                       "match_val": "AUTH_SUCCESS"
                     }
                   ],
                   "condition_expression": "1"
                 }
               ]
             },
             {
               "id": "tunnel_absence_group",
               "source": ["tunnel_log"],
               "rules": [
                 {
                   "id": "no_tunnel_open",
                   "times": [1, 1],
                   "greedy": false,
                   "optional": false,
                   "one_or_more": false,
                   "follow": true,
                   "not": true,
                   "condition": [
                     {
                       "id": "1",
                       "type": "match",
                       "match_key": "res",
                       "match_type": "=",
                       "match_val": "OPEN"
                     }
                   ],
                   "condition_expression": "1"
                 }
               ]
             },
             {
               "id": "auth_success_again_group",
               "source": ["auth_log"],
               "rules": [
                 {
                   "id": "auth_success_again_event",
                   "times": [1, 1],
                   "greedy": false,
                   "optional": false,
                   "one_or_more": false,
                   "follow": true,
                   "not": false,
                   "condition": [
                     {
                       "id": "1",
                       "type": "match",
                       "match_key": "res",
                       "match_type": "=",
                       "match_val": "AUTH_SUCCESS"
                     }
                   ],
                   "condition_expression": "1"
                 }
               ]
             }
           ]'::jsonb,
           '{"time": 10, "unit": "s"}'::jsonb,
           1
       );
```

#### 匹配逻辑说明
1. **第一步**: 检测到用户认证成功 (AUTH_SUCCESS)
2. **第二步**: 在后续时间内没有检测到该用户的隧道打开操作 (not OPEN)
3. **时间窗口**: 整个检测必须在10秒内完成

#### 匹配示例数据序列
```
用户user001在10秒内:
10:30:00 - auth_log: AUTH_SUCCESS
10:30:01~10:30:10 - 无tunnel_log的OPEN事件 ← 触发告警
```

#### 非匹配示例数据序列
```
用户user001在10秒内:
10:30:00 - auth_log: AUTH_SUCCESS
10:30:05 - tunnel_log: OPEN ← 不会触发告警，正常流程
```

## 测试数据生成建议

### 测试用例1: 反复隧道连接

#### 正向测试（应该匹配）
```json
{"__log_type__": "tunnel_log", "__timestamp__": "2024-01-15T10:30:00.000Z", "user_id": "test_user_1", "res": "OPEN"}
{"__log_type__": "tunnel_log", "__timestamp__": "2024-01-15T10:30:15.000Z", "user_id": "test_user_1", "res": "CLOSE"}
{"__log_type__": "tunnel_log", "__timestamp__": "2024-01-15T10:30:30.000Z", "user_id": "test_user_1", "res": "OPEN"}
```

#### 反向测试（不应该匹配）
```json
{"__log_type__": "tunnel_log", "__timestamp__": "2024-01-15T10:30:00.000Z", "user_id": "test_user_2", "res": "OPEN"}
{"__log_type__": "tunnel_log", "__timestamp__": "2024-01-15T10:31:30.000Z", "user_id": "test_user_2", "res": "CLOSE"}
```

### 测试用例2: 认证成功但连接失败

#### 正向测试（应该匹配）
```json
{"__log_type__": "auth_log", "__timestamp__": "2024-01-15T10:40:00.000Z", "user_id": "test_user_3", "res": "AUTH_SUCCESS"}
// 10秒内无tunnel_log的OPEN事件
```

#### 反向测试（不应该匹配）
```json
{"__log_type__": "auth_log", "__timestamp__": "2024-01-15T10:50:00.000Z", "user_id": "test_user_4", "res": "AUTH_SUCCESS"}
{"__log_type__": "tunnel_log", "__timestamp__": "2024-01-15T10:50:05.000Z", "user_id": "test_user_4", "res": "OPEN"}
```

## 运行测试

### 启动规则1测试
```bash
java -cp target/flink-quickstart-1.0-SNAPSHOT.jar org.example.DynamicCEPJob \
  --rule.id "tunnel_repeated_operations" \
  --db.url "******************************************" \
  --db.user "admin" \
  --db.password "password" \
  --nats.url "nats://localhost:4222" \
  --nats.subject "log_events"
```

### 启动规则2测试
```bash
java -cp target/flink-quickstart-1.0-SNAPSHOT.jar org.example.DynamicCEPJob \
  --rule.id "auth_success_tunnel_fail" \
  --db.url "******************************************" \
  --db.user "admin" \
  --db.password "password" \
  --nats.url "nats://localhost:4222" \
  --nats.subject "log_events"
```

## 预期告警输出

### 规则1告警示例
```
规则配置 tunnel_repeated_operations 完全匹配成功，分组键: user_id，分组值: test_user_1，
时间窗口: {"time": 60, "unit": "s"}，匹配步骤: [step_tunnel_operation_group_tunnel_open, step_tunnel_operation_group_tunnel_close, step_tunnel_operation_group_tunnel_open_again]，
总匹配事件数: 3
```

### 规则2告警示例
```
规则配置 auth_success_tunnel_fail 完全匹配成功，分组键: user_id，分组值: test_user_3，
时间窗口: {"time": 10, "unit": "s"}，匹配步骤: [step_auth_success_group_auth_success_event, step_tunnel_absence_group_no_tunnel_open]，
总匹配事件数: 1
```

## 扩展测试建议

1. **并发测试**: 多个用户同时进行操作，验证keyBy分组功能
2. **边界测试**: 在时间窗口边界进行测试
3. **大数据量测试**: 发送大量日志验证性能
4. **异常数据测试**: 发送格式异常的日志验证容错性
5. **长时间运行测试**: 验证系统稳定性和内存管理

## 故障排查

### 常见问题
1. **规则不匹配**: 检查时间戳格式和时间窗口设置
2. **用户ID不匹配**: 确认user_id字段值完全一致
3. **时序问题**: 确认事件按正确时间顺序发送
4. **数据源过滤**: 检查__log_type__字段值是否正确

### 调试建议
1. 开启详细日志输出
2. 使用较短的时间窗口进行测试
3. 单独测试每个规则
4. 验证数据库连接和NATS连接
5. 检查告警表中的输出结果 