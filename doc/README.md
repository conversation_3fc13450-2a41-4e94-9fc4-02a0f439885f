# DynamicCEPJob 架构图表集合

本目录包含了DynamicCEPJob系统的所有架构图表，使用Mermaid格式编写，可以在支持Mermaid的环境中渲染。

## 📊 图表列表

### 1. [系统架构图](./系统架构图.md)
- **用途**：展示整体系统架构和组件关系
- **包含组件**：DMP、X-MSG、XMSG Client、Flink集群、数据源、输出端
- **适用场景**：系统概览、技术方案介绍

### 2. [数据流时序图](./数据流时序图.md)
- **用途**：展示组件间的交互时序
- **关键流程**：规则配置 → 任务启动 → 数据处理 → 状态反馈
- **适用场景**：流程说明、问题排查

### 3. [CEP处理流程图](./CEP处理流程图.md)
- **用途**：展示单个CEP作业的内部处理流程
- **核心逻辑**：配置加载 → 数据流处理 → 模式匹配 → 结果输出
- **适用场景**：代码理解、性能优化

### 3. [DynamicCEPJob使用指南.md](./DynamicCEPJob使用指南.md)
- **用途**：展示规则，数据源消息的规范及示例
- **核心逻辑**：数据源事件规范 → 规则规范
- **适用场景**：数据源事件规范、规则规范

### 4. [测试文档](./测试场景文档.md)
- **用途**：场景测试
- **测试数据**：[测试数据](./test_rules_setup.sql)

### 5. [监控](./flink_job.md)
- **用途**：任务监控

## 🛠️ 使用方法

### 在线渲染
1. **GitHub**：直接在GitHub仓库中查看（支持Mermaid渲染）
2. **GitLab**：在GitLab中查看Markdown文件
3. **在线编辑器**：
   - [Mermaid Live Editor](https://mermaid.live/)
   - [Mermaid Chart](https://www.mermaidchart.com/)

### 本地工具
1. **VS Code插件**：
   ```bash
   # 安装Mermaid预览插件
   code --install-extension bierner.markdown-mermaid
   ```

2. **Typora**：支持Mermaid图表的Markdown编辑器

3. **命令行工具**：
   ```bash
   # 安装mermaid-cli
   npm install -g @mermaid-js/mermaid-cli
   
   # 生成PNG图片
   mmdc -i 系统架构图.md -o 系统架构图.png
   
   # 生成SVG图片
   mmdc -i 系统架构图.md -o 系统架构图.svg
   ```

### 集成到文档

**在Markdown中引用：**
```markdown
```mermaid
# 复制对应文件中的mermaid代码块内容
```

**在Confluence中使用：**
1. 安装Mermaid插件
2. 创建Mermaid宏
3. 粘贴图表代码

**在Notion中使用：**
1. 使用代码块，语言设置为mermaid
2. 或使用第三方集成工具

## 📝 图表维护

### 修改图表
1. 编辑对应的.md文件中的mermaid代码
2. 在Mermaid Live Editor中预览效果
3. 确认无语法错误后保存

### 添加新图表
1. 创建新的.md文件
2. 按照现有格式编写内容
3. 更新本README文件

### 版本控制
- 所有图表文件都纳入Git版本控制
- 重大修改时创建新的版本分支
- 保持图表与代码的同步更新

## 🎨 样式说明

### 颜色规范
- **蓝色 (#e1f5fe)**：当前已实现的功能
- **橙色 (#fff3e0)**：未来可扩展的功能
- **紫色 (#f3e5f5)**：核心处理组件
- **绿色 (#e8f5e8)**：应用层组件
- **浅蓝色 (#e3f2fd)**：数据层组件

### 线条类型
- **实线**：当前数据流和确定关系
- **虚线**：扩展数据流或依赖关系

### 图形类型
- **矩形**：普通组件
- **圆角矩形**：处理步骤
- **菱形**：决策点
- **圆柱形**：数据存储
- **圆形**：开始/结束

## 🔧 故障排除

### 常见问题

**图表不显示：**
1. 检查Mermaid语法是否正确
2. 确认平台是否支持Mermaid渲染
3. 尝试在Mermaid Live Editor中验证

**语法错误：**
1. 检查节点名称是否包含特殊字符
2. 确认箭头和连接语法正确
3. 验证子图和样式定义语法

**渲染效果不佳：**
1. 调整节点标签长度
2. 优化图表布局和连接
3. 使用合适的图表类型

### 技术支持

- **Mermaid官方文档**：https://mermaid.js.org/
- **语法参考**：https://mermaid.js.org/syntax/
- **在线编辑器**：https://mermaid.live/

## 📄 文档关联

- [部署手册](../部署手册.md)
- [项目初始化](../项目初始化.md)
