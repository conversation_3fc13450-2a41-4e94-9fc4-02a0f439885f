# DynamicCEPJob处理流程图

```mermaid
flowchart TD
    START([开始]) --> LOAD[从PostgreSQL<br/>加载规则配置]
    LOAD --> PARSE[解析JSONB配置<br/>rules字段和within字段]
    PARSE --> CONNECT["连接数据源<br/>(当前为NATS，kafka，后续扩展其他)"]
    CONNECT --> STREAM[创建数据流]
    STREAM --> KEYBY[根据group_key进行keyBy分组]
    KEYBY --> PATTERN[构建CEP Pattern<br/>组合规则组和条件表达式]
    PATTERN --> CEP[应用CEP PatternStream]
    CEP --> MATCH{模式匹配}
    MATCH -->|成功| ALERT[生成告警事件AlertEvent]
    MATCH -->|失败| STREAM
    ALERT --> OUTPUT["输出<br/>(当前为PostgreSQL，后续扩展其他)"]
    OUTPUT --> PRINT[控制台输出]
    PRINT --> STREAM
    
    subgraph "条件表达式处理"
        EXPR[条件表达式解析]
        LEXER[词法分析器Token化]
        PARSER[语法分析器AST构建]
        EVAL[表达式求值器递归计算]
        
        EXPR --> LEXER
        LEXER --> PARSER
        PARSER --> EVAL
    end
    
    PATTERN -.-> EXPR
    
    classDef process fill:#e3f2fd
    classDef decision fill:#fff3e0
    classDef data fill:#f3e5f5
    
    class LOAD,PARSE,CONNECT,STREAM,KEYBY,PATTERN,CEP,ALERT,OUTPUT,PRINT process
    class MATCH decision
    class EXPR,LEXER,PARSER,EVAL data
```

## 流程说明

### 主要处理步骤

1. **配置加载**：从PostgreSQL数据库加载规则配置
2. **配置解析**：解析JSONB格式的rules和within字段
3. **数据源连接**：建立与数据源的的连接，当前为nats。
4. **数据流创建**：创建Flink DataStream处理管道
5. **数据分组**：根据group_key进行keyBy操作
6. **模式构建**：根据规则配置构建CEP Pattern
7. **CEP应用**：将Pattern应用到数据流形成PatternStream
8. **模式匹配**：执行复杂事件匹配逻辑
9. **告警生成**：匹配成功时生成AlertEvent
10. **结果输出**：输出到PostgreSQL和控制台

### 条件表达式处理

CEP规则中的条件表达式需要经过以下处理：

- **词法分析**：将表达式字符串转换为Token序列
- **语法分析**：构建抽象语法树（AST）
- **表达式求值**：递归计算表达式结果
- **支持语法**：AND、OR、NOT、括号嵌套

### 关键特性

- **流式处理**：持续不断处理实时数据流
- **状态管理**：维护CEP匹配状态
- **容错机制**：支持检查点和故障恢复
- **动态配置**：支持运行时规则更新 