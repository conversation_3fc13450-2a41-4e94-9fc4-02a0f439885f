# DynamicCEPJob使用指南

## 概述

DynamicCEPJob是一个基于Apache Flink CEP的动态规则处理系统，支持从PostgreSQL数据库加载规则配置，从NATS消息队列或Kafka消息队列获取日志数据，并进行实时的复杂事件处理。

## 核心特性

1. **动态规则配置**：规则存储在PostgreSQL数据库中，支持运行时加载
2. **复杂条件表达式**：支持AND、OR、NOT逻辑操作符及嵌套括号
3. **多消息队列支持**：支持NATS和Kafka两种消息队列作为数据源
4. **多数据源支持**：可以处理多种类型的日志数据源
5. **灵活的Pattern配置**：支持times、greedy、optional、oneOrMore等CEP配置
6. **统一数据流处理**：所有规则在单一stream上处理，提高效率
7. **JSONB存储**：使用PostgreSQL的JSONB格式存储配置，支持高效查询

## 系统架构

```
[NATS消息队列]
               ↘
                [Flink CEP] → [PostgreSQL告警表]
               ↗         ↑
[Kafka消息队列]     [PostgreSQL规则表]
```

系统支持两种消息队列作为数据源：
- **NATS**：轻量级消息队列，适合高频小消息场景
- **Kafka**：分布式消息队列，适合大规模数据流处理场景

## 数据库表结构

- [建表语句](../cep_create_tables.sql)

### zj_cep_rules表（规则配置表）

```sql
CREATE TABLE zj_cep_rules (
    id VARCHAR(255) PRIMARY KEY,                    -- 规则配置ID，唯一标识
    group_key VARCHAR(255),                         -- 用于keyBy的字段名
    rules JSONB NOT NULL,                           -- JSONB格式的规则配置
    within JSONB,                                   -- JSONB格式的CEP时间窗口配置
    status INTEGER DEFAULT 1,                      -- 规则状态：1-启用，0-禁用
    add_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,   -- 规则添加时间
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 规则更新时间
);
```

#### 字段说明

- **id**: 规则配置的唯一标识符，用于启动特定的CEP作业
- **group_key**: 用于keyBy操作的字段名，如`user_id`、`client_ip`、`session_id`等
- **rules**: JSONB格式的规则配置，包含规则组、数据源、子规则、条件表达式等
- **within**: JSONB格式的CEP时间窗口配置，用于Pattern的within()方法
- **status**: 规则状态，1表示启用，0表示禁用
- **add_time**: 规则首次创建时间
- **update_time**: 规则最后修改时间

### zj_cep_alert_events表（告警事件表）

```sql
CREATE TABLE zj_cep_alert_events (
    id SERIAL PRIMARY KEY,                          -- 自增主键ID
    group_key VARCHAR(255),                         -- 分组键值
    alert_type VARCHAR(100) NOT NULL,               -- 告警类型
    message TEXT,                                   -- 告警消息
    timestamp TIMESTAMP NOT NULL,                   -- 告警发生时间
    rule_id VARCHAR(255) NOT NULL,                  -- 触发告警的规则ID
    alert_content JSONB,                            -- 告警详细内容
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 记录创建时间
);
```

## 配置格式说明

### rules字段配置格式

```json
[
  {
    "id": "规则组ID",
    "source": ["数据源类型1", "数据源类型2"],
    "rules": [
      {
        "id": "子规则ID",
        "times": [最小次数, 最大次数],
        "greedy": false,
        "optional": false,
        "one_or_more": false,
        "follow": true,
        "not": false,
        "condition": [
          {
            "id": "条件ID",
            "type": "match",
            "match_key": "匹配字段名",
            "match_type": "匹配类型",
            "match_val": "匹配值"
          }
        ],
        "condition_expression": "条件逻辑表达式"
      }
    ]
  }
]
```

### within字段配置格式

```json
{
  "time": 时间数值,
  "unit": "时间单位"
}
```

支持的时间单位：
- `s`、`second`、`seconds`：秒
- `m`、`minute`、`minutes`：分钟
- `h`、`hour`、`hours`：小时
- `d`、`day`、`days`：天

### 数据源配置说明

- **数组格式**：`["auth_log", "api_log", "tunnel_log"]`
- **单一数据源**：`["auth_log"]`
- **空数组**：`[]` - 匹配所有数据源的事件
- **匹配逻辑**：事件的logType字段只要匹配数组中任一元素即可

### Pattern配置说明

- **times**: `[min, max]` - 指定事件出现次数范围
- **greedy**: `true/false` - 是否使用贪婪匹配
- **optional**: `true/false` - 是否为可选匹配
- **one_or_more**: `true/false` - 是否为oneOrMore匹配
- **follow**: `true/false` - 事件连接方式，true使用followedBy（允许中间有其他事件），false使用next（严格相邻）
- **not**: `true/false` - 是否为否定模式，true使用notFollowedBy或notNext，false使用正常的followedBy或next

#### Pattern连接方式说明

根据**follow**和**not**属性的组合，系统会选择不同的Pattern连接方式：

| follow | not | 使用的方法 | 说明 |
|--------|-----|----------|------|
| false | false | `next()` | 严格相邻的下一个事件 |
| true | false | `followedBy()` | 允许中间有其他事件的后续事件 |
| false | true | `notNext()` | 下一个事件不能是指定模式 |
| true | true | `notFollowedBy()` | 后续不能出现指定模式的事件 |

**默认值**：如果未指定，follow默认为true（使用followedBy），not默认为false（正常模式）

### 匹配类型说明

#### 字符串匹配
- `=`、`eq`、`equals`：完全相等
- `!=`、`ne`、`not_equals`：不相等
- `contains`：包含子字符串
- `not_contains`：不包含子字符串
- `starts_with`、`startswith`：以指定字符串开头
- `ends_with`、`endswith`：以指定字符串结尾
- `regex`、`matches`：正则表达式匹配

#### 数值比较
- `>`、`gt`：大于
- `<`、`lt`：小于
- `>=`、`ge`、`gte`：大于等于
- `<=`、`le`、`lte`：小于等于

### 条件表达式语法

#### 基本语法
- **操作符优先级**：`NOT` > `AND` > `OR`
- **括号支持**：支持任意深度的嵌套括号
- **大小写不敏感**：`AND`、`and`、`And`都可以

#### 表达式示例
```
# 简单表达式
1                           # 条件1为真
NOT 1                       # 条件1为假

# 组合表达式
1 AND 2                     # 条件1和条件2都为真
1 OR 2                      # 条件1或条件2为真
1 AND NOT 2                 # 条件1为真且条件2为假

# 括号表达式
(1 OR 2) AND 3              # (条件1或条件2) 且条件3
1 OR (2 AND 3)              # 条件1 或 (条件2且条件3)

# 复杂嵌套
(1 AND (2 OR (3 AND 4)))    # 支持任意深度嵌套
((1 OR 2) AND (3 OR 4))     # 多重括号组合
```

## 完整配置示例

### 示例1：用户认证失败监控

```sql
INSERT INTO zj_cep_rules (id, group_key, rules, within, status) 
VALUES (
    'auth_failure_monitor',
    'user_id',
    '[
      {
        "id": "auth_group",
        "source": ["auth_log"],
        "rules": [
          {
            "id": "failed_attempts",
            "times": [3, 999],
            "greedy": false,
            "optional": false,
            "one_or_more": false,
            "follow": true,
            "not": false,
            "condition": [
              {
                "id": "1",
                "type": "match",
                "match_key": "status",
                "match_type": "=",
                "match_val": "FAILED"
              }
            ],
            "condition_expression": "1"
          },
          {
            "id": "success_after_failures",
            "times": [1, 1],
            "greedy": false,
            "optional": false,
            "one_or_more": false,
            "follow": true,
            "not": false,
            "condition": [
              {
                "id": "1",
                "type": "match",
                "match_key": "status",
                "match_type": "=",
                "match_val": "SUCCESS"
              }
            ],
            "condition_expression": "1"
          }
        ]
      }
    ]'::jsonb,
    '{"time": 60, "unit": "s"}'::jsonb,
    1
);
```

**功能说明**：监控用户连续3次或以上认证失败后的成功登录，在60秒时间窗口内检测。

### 示例2：高频API调用监控

```sql
INSERT INTO zj_cep_rules (id, group_key, rules, within, status) 
VALUES (
    'api_frequency_monitor',
    'client_ip',
    '[
      {
        "id": "api_group",
        "source": ["api_log"],
        "rules": [
          {
            "id": "high_frequency_calls",
            "times": [10, 999],
            "greedy": false,
            "optional": false,
            "one_or_more": false,
            "follow": true,
            "not": false,
            "condition": [
              {
                "id": "1",
                "type": "match",
                "match_key": "endpoint",
                "match_type": "contains",
                "match_val": "/api/"
              },
              {
                "id": "2",
                "type": "match",
                "match_key": "response_time",
                "match_type": ">",
                "match_val": "1000"
              }
            ],
            "condition_expression": "1 AND 2"
          }
        ]
      }
    ]'::jsonb,
    '{"time": 300, "unit": "s"}'::jsonb,
    1
);
```

**功能说明**：监控客户端IP对包含"/api/"的接口进行高频调用（10次以上），且响应时间超过1000ms的情况。

### 示例3：多数据源安全事件关联

```sql
INSERT INTO zj_cep_rules (id, group_key, rules, within, status) 
VALUES (
    'security_correlation_analysis',
    'session_id',
    '[
      {
        "id": "security_group",
        "source": ["tunnel_log", "auth_log", "business_log"],
        "rules": [
          {
            "id": "suspicious_tunnel",
            "times": [1, 1],
            "greedy": false,
            "optional": false,
            "one_or_more": false,
            "condition": [
              {
                "id": "1",
                "type": "match",
                "match_key": "event_type",
                "match_type": "=",
                "match_val": "TUNNEL_ESTABLISHED"
              },
              {
                "id": "2",
                "type": "match",
                "match_key": "source_ip",
                "match_type": "regex",
                "match_val": "^(10\\.|192\\.168\\.|172\\.(1[6-9]|2[0-9]|3[01])\\.).*"
              }
            ],
            "condition_expression": "1 AND NOT 2"
          },
          {
            "id": "privilege_escalation",
            "times": [1, 5],
            "greedy": false,
            "optional": true,
            "one_or_more": false,
            "condition": [
              {
                "id": "1",
                "type": "match",
                "match_key": "action",
                "match_type": "contains",
                "match_val": "privilege"
              },
              {
                "id": "2",
                "type": "match",
                "match_key": "result",
                "match_type": "=",
                "match_val": "success"
              }
            ],
            "condition_expression": "1 AND 2"
          }
        ]
      }
    ]'::jsonb,
    '{"time": 600, "unit": "s"}'::jsonb,
    1
);
```

**功能说明**：检测可疑的隧道建立（非内网IP建立隧道）以及可选的权限提升操作。

### 示例4：Pattern连接方式演示

```sql
INSERT INTO zj_cep_rules (id, group_key, rules, within, status) 
VALUES (
    'pattern_connection_demo',
    'user_id',
    '[
      {
        "id": "connection_demo_group",
        "source": ["auth_log", "api_log"],
        "rules": [
          {
            "id": "login_success",
            "times": [1, 1],
            "greedy": false,
            "optional": false,
            "one_or_more": false,
            "follow": true,
            "not": false,
            "condition": [
              {
                "id": "1",
                "type": "match",
                "match_key": "action",
                "match_type": "=",
                "match_val": "LOGIN"
              },
              {
                "id": "2",
                "type": "match",
                "match_key": "status",
                "match_type": "=",
                "match_val": "SUCCESS"
              }
            ],
            "condition_expression": "1 AND 2"
          },
          {
            "id": "no_immediate_logout",
            "times": [1, 1],
            "greedy": false,
            "optional": false,
            "one_or_more": false,
            "follow": false,
            "not": true,
            "condition": [
              {
                "id": "1",
                "type": "match",
                "match_key": "action",
                "match_type": "=",
                "match_val": "LOGOUT"
              }
            ],
            "condition_expression": "1"
          },
          {
            "id": "api_access",
            "times": [1, 999],
            "greedy": false,
            "optional": false,
            "one_or_more": false,
            "follow": true,
            "not": false,
            "condition": [
              {
                "id": "1",
                "type": "match",
                "match_key": "endpoint",
                "match_type": "contains",
                "match_val": "/api/"
              }
            ],
            "condition_expression": "1"
          }
        ]
      }
    ]'::jsonb,
    '{"time": 300, "unit": "s"}'::jsonb,
    1
);
```

**功能说明**：
- **第一步**：用户成功登录（使用默认的followedBy连接）
- **第二步**：下一个事件不能是立即退出（使用notNext，follow=false, not=true）
- **第三步**：后续有API访问行为（使用followedBy，允许中间有其他事件）

**Pattern连接逻辑**：
1. `login_success` → `no_immediate_logout`：使用notNext()，严格检查下一个事件不是LOGOUT
2. `no_immediate_logout` → `api_access`：使用followedBy()，允许中间有其他事件的API访问

这个示例展示了如何组合使用follow和not属性来构建复杂的事件序列检测。

## 作业启动方式

### 命令行参数

#### 使用NATS数据源（默认）

```bash
java -cp target/flink-quickstart-1.0-SNAPSHOT.jar org.example.DynamicCEPJob \
  --rule.id "规则ID" \
  --source.type "nats" \
  --db.url "************************************" \
  --db.user "用户名" \
  --db.password "密码" \
  --nats.url "nats://host:port" \
  --nats.subject "主题名" \
  --nats.username "NATS用户名" \
  --nats.password "NATS密码" \
  --nats.group "NATS消费者组"
```

#### 使用Kafka数据源

```bash
java -cp target/flink-quickstart-1.0-SNAPSHOT.jar org.example.DynamicCEPJob \
  --rule.id "规则ID" \
  --source.type "kafka" \
  --db.url "************************************" \
  --db.user "用户名" \
  --db.password "密码" \
  --kafka.bootstrap.servers "kafka-server:9092" \
  --kafka.topic "主题名" \
  --kafka.group.id "Kafka消费者组ID" \
  --kafka.auto.offset.reset "latest"
```

### 参数说明

#### 通用参数

- **rule.id**（必需）：要执行的规则配置ID
- **source.type**（可选）：数据源类型，支持 `nats` 或 `kafka`，默认为 `nats`
- **db.url**（可选）：PostgreSQL数据库连接URL，默认值见代码
- **db.user**（可选）：数据库用户名，默认为admin
- **db.password**（可选）：数据库密码

#### NATS相关参数

- **nats.url**（可选）：NATS服务器URL，默认为 `nats://***************:4242`
- **nats.subject**（可选）：NATS主题名，默认为 `log_events`
- **nats.username**（可选）：NATS用户名，默认为 `dialer_abc`
- **nats.password**（可选）：NATS密码，默认为 `efgh1234`
- **nats.group**（可选）：NATS消费者组名称，用于负载均衡，默认为 `flink_consumer`

#### Kafka相关参数

- **kafka.bootstrap.servers**（可选）：Kafka服务器地址列表，默认为 `***************:9092`
- **kafka.topic**（可选）：Kafka主题名，默认为 `log_events`
- **kafka.group.id**（可选）：Kafka消费者组ID，用于负载均衡，默认为 `flink_consumer_group`
- **kafka.auto.offset.reset**（可选）：偏移量重置策略，支持 `earliest`（从最早消息开始）或 `latest`（从最新消息开始），默认为 `latest`

## 输入数据规范

### LogEvent数据结构

系统通过NATS或Kafka接收JSON格式的日志事件，每条消息必须包含以下核心字段：

```json
{
  "__log_type__": "数据源类型标识",
  "__timestamp__": "事件时间戳",
  "业务字段1": "业务数据",
  "业务字段2": "业务数据",
  ...
}
```

#### 必需字段说明

- **__log_type__**：数据源类型标识，用于匹配规则配置中的source字段，使用双下划线前缀避免与业务字段冲突
- **__timestamp__**：事件发生时间，支持ISO8601格式，使用双下划线前缀避免与业务字段冲突

#### 可选字段说明

- **分组字段**：用于keyBy操作的字段，字段名由规则配置中的group_key指定，可以是任意业务字段
- **其他业务字段**：根据具体业务需求添加，字段名可以任意定义，不受限制

### 消息示例

以下示例适用于NATS和Kafka两种消息队列：

#### 示例1：认证日志事件

```json
{
  "__log_type__": "auth_log",
  "__timestamp__": "2024-01-15T10:30:45.123Z",
  "user_id": "user_12345",
  "session_id": "sess_abcdef123456",
  "client_ip": "*************",
  "action": "LOGIN",
  "status": "FAILED",
  "reason": "INVALID_PASSWORD",
  "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
  "source_country": "CN",
  "attempt_count": 3,
  "level": "WARN",
  "message": "用户登录失败：密码错误"
}
```

#### 示例2：API访问日志事件

```json
{
  "__log_type__": "api_log",
  "__timestamp__": "2024-01-15T10:31:02.456Z",
  "client_ip": "************",
  "request_id": "req_789xyz456",
  "method": "POST",
  "endpoint": "/api/v1/users/profile",
  "status_code": 200,
  "response_time": 1250,
  "user_id": "user_67890",
  "content_length": 2048,
  "referer": "https://example.com/dashboard",
  "level": "INFO",
  "message": "API请求处理完成"
}
```

#### 示例3：隧道连接日志事件

```json
{
  "__log_type__": "tunnel_log",
  "__timestamp__": "2024-01-15T10:32:15.789Z",
  "session_id": "tunnel_session_001",
  "event_type": "TUNNEL_ESTABLISHED",
  "source_ip": "*********",
  "target_ip": "************",
  "source_port": 45123,
  "target_port": 22,
  "protocol": "SSH",
  "encryption": "AES256",
  "user_id": "admin_user",
  "connection_id": "conn_abc123",
  "level": "INFO",
  "message": "SSH隧道建立成功"
}
```

#### 示例4：业务操作日志事件

```json
{
  "__log_type__": "business_log",
  "__timestamp__": "2024-01-15T10:33:30.012Z",
  "user_id": "user_admin001",
  "session_id": "sess_business_789",
  "action": "privilege_escalation",
  "resource": "/admin/users",
  "result": "success",
  "previous_role": "user",
  "new_role": "admin",
  "approver": "super_admin",
  "ip_address": "*************",
  "level": "WARN",
  "message": "用户权限提升操作成功"
}
```

#### 示例5：系统异常日志事件

```json
{
  "__log_type__": "system_log",
  "__timestamp__": "2024-01-15T10:34:45.345Z",
  "server_id": "web-server-01",
  "component": "authentication_service",
  "event_type": "EXCEPTION",
  "exception_class": "java.sql.SQLException",
  "error_code": "CONNECTION_TIMEOUT",
  "error_message": "Database connection timeout after 30 seconds",
  "stack_trace": "java.sql.SQLException: Connection timeout...",
  "request_id": "req_error_123",
  "level": "ERROR",
  "message": "数据库连接超时异常"
}
```

### 数据类型支持

#### 字符串类型
```json
{
  "user_name": "张三",
  "action": "LOGIN",
  "status": "SUCCESS"
}
```

#### 数值类型
```json
{
  "response_time": 1250,
  "status_code": 200,
  "attempt_count": 3,
  "file_size": 2048.5
}
```

#### 布尔类型
```json
{
  "is_admin": true,
  "ssl_enabled": false,
  "encrypted": true
}
```

#### 数组类型
```json
{
  "tags": ["security", "auth", "critical"],
  "affected_systems": ["web", "database", "cache"],
  "error_codes": [4001, 4002, 5001]
}
```

#### 嵌套对象类型
```json
{
  "user_info": {
    "id": "user_123",
    "name": "张三",
    "department": "IT部门",
    "roles": ["user", "developer"]
  },
  "request_details": {
    "headers": {
      "user-agent": "Chrome/96.0",
      "accept": "application/json"
    },
    "body_size": 1024
  }
}
```

### 时间戳格式

#### ISO8601格式
```json
{
  "__timestamp__": "2024-01-15T10:30:45.123Z",          // UTC时间
  "__timestamp__": "2024-01-15T18:30:45.123+08:00"     // 带时区
}
```

### 消息队列发布示例

#### Kafka发布示例

##### Java发布代码示例（Kafka）

```java
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.Properties;
import java.util.HashMap;
import java.util.Map;
import java.time.Instant;

public class KafkaLogPublisher {
    private KafkaProducer<String, String> producer;
    private ObjectMapper objectMapper = new ObjectMapper();
    private String topicName;
    
    public KafkaLogPublisher(String bootstrapServers, String topicName) {
        this.topicName = topicName;
        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.ACKS_CONFIG, "all");
        props.put(ProducerConfig.RETRIES_CONFIG, 3);
        
        this.producer = new KafkaProducer<>(props);
    }
    
    public void publishAuthLog(String userId, String action, String status) {
        try {
            Map<String, Object> logEvent = new HashMap<>();
            logEvent.put("__log_type__", "auth_log");
            logEvent.put("__timestamp__", Instant.now().toString());
            logEvent.put("user_id", userId);
            logEvent.put("action", action);
            logEvent.put("status", status);
            logEvent.put("client_ip", getClientIP());
            logEvent.put("level", status.equals("SUCCESS") ? "INFO" : "WARN");
            
            String jsonMessage = objectMapper.writeValueAsString(logEvent);
            ProducerRecord<String, String> record = new ProducerRecord<>(topicName, userId, jsonMessage);
            
            producer.send(record, (metadata, exception) -> {
                if (exception != null) {
                    exception.printStackTrace();
                } else {
                    System.out.println("消息发送成功: " + metadata.toString());
                }
            });
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    public void close() {
        producer.close();
    }
}
```

##### Python发布代码示例（Kafka）

```python
from kafka import KafkaProducer
import json
from datetime import datetime

class KafkaLogPublisher:
    def __init__(self, bootstrap_servers, topic_name):
        self.topic_name = topic_name
        self.producer = KafkaProducer(
            bootstrap_servers=bootstrap_servers,
            value_serializer=lambda v: json.dumps(v).encode('utf-8'),
            key_serializer=lambda k: k.encode('utf-8') if k else None,
            acks='all',
            retries=3
        )
    
    def publish_api_log(self, client_ip, endpoint, response_time, status_code, user_id=None):
        log_event = {
            "__log_type__": "api_log",
            "__timestamp__": datetime.utcnow().isoformat() + "Z",
            "client_ip": client_ip,
            "endpoint": endpoint,
            "response_time": response_time,
            "status_code": status_code,
            "level": "INFO" if status_code < 400 else "ERROR"
        }
        
        if user_id:
            log_event["user_id"] = user_id
        
        # 使用 user_id 作为 key 来确保同一用户的消息发送到同一分区
        key = user_id if user_id else client_ip
        
        future = self.producer.send(self.topic_name, value=log_event, key=key)
        try:
            metadata = future.get(timeout=10)
            print(f"消息发送成功: topic={metadata.topic}, partition={metadata.partition}, offset={metadata.offset}")
        except Exception as e:
            print(f"消息发送失败: {e}")
    
    def close(self):
        self.producer.close()

# 使用示例
publisher = KafkaLogPublisher("localhost:9092", "log_events")
publisher.publish_api_log("*************", "/api/users", 1250, 200, "user_123")
publisher.close()
```

#### NATS发布示例

#### Java发布代码示例

```java
import io.nats.client.*;
import com.fasterxml.jackson.databind.ObjectMapper;

public class NatsLogPublisher {
    private Connection natsConnection;
    private ObjectMapper objectMapper = new ObjectMapper();
    
         public void publishAuthLog(String userId, String action, String status) {
         try {
             Map<String, Object> logEvent = new HashMap<>();
             logEvent.put("__log_type__", "auth_log");
             logEvent.put("__timestamp__", Instant.now().toString());
             logEvent.put("user_id", userId);
             logEvent.put("action", action);
             logEvent.put("status", status);
             logEvent.put("client_ip", getClientIP());
             logEvent.put("level", status.equals("SUCCESS") ? "INFO" : "WARN");
            
            String jsonMessage = objectMapper.writeValueAsString(logEvent);
            natsConnection.publish("log_events", jsonMessage.getBytes());
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
```

#### Python发布代码示例

```python
import asyncio
import json
from datetime import datetime
from nats.aio.client import Client as NATS

async def publish_api_log(client_ip, endpoint, response_time, status_code):
    nc = NATS()
    await nc.connect("nats://localhost:4222")
    
         log_event = {
         "__log_type__": "api_log",
         "__timestamp__": datetime.utcnow().isoformat() + "Z",
         "client_ip": client_ip,
         "endpoint": endpoint,
         "response_time": response_time,
         "status_code": status_code,
         "level": "INFO" if status_code < 400 else "ERROR"
     }
    
    await nc.publish("log_events", json.dumps(log_event).encode())
    await nc.close()

# 使用示例
asyncio.run(publish_api_log("*************", "/api/users", 1250, 200))
```

### 数据质量要求

#### 必须满足的要求

1. **JSON格式合法**：确保消息为有效的JSON格式
2. **必需字段完整**：__log_type__和__timestamp__字段必须存在
3. **数据类型正确**：字段值类型与预期匹配
4. **编码格式**：使用UTF-8编码

#### 建议遵循的要求

1. **时间戳准确性**：使用准确的事件发生时间
2. **字段命名规范**：使用snake_case命名风格
3. **数据一致性**：同类型事件的字段结构保持一致
4. **适当的日志级别**：根据事件重要性设置合适的level

### 性能考虑

#### 消息大小建议
- **单条消息**：建议小于64KB
- **批量发送**：支持批量发送以提高性能
- **压缩传输**：对大消息可启用压缩

#### 发送频率建议
- **高频场景**：使用异步发送避免阻塞
- **批量聚合**：相似事件可在客户端聚合后发送
- **背压处理**：实现适当的背压和重试机制

## 数据流处理流程

1. **配置加载**：从PostgreSQL数据库加载指定ID的规则配置
2. **数据源连接**：根据source.type参数连接到NATS或Kafka消息队列获取日志事件流
3. **数据解析**：将消息队列中的消息解析为LogEvent对象
4. **数据分组**：根据group_key字段对事件流进行keyBy分组
5. **CEP模式匹配**：应用组合的Pattern进行复杂事件处理
6. **时间窗口限制**：使用within配置限制匹配的时间范围
7. **告警生成**：当模式匹配成功时生成告警事件
8. **结果输出**：将告警事件输出到控制台和PostgreSQL数据库

## JSONB查询示例

利用PostgreSQL的JSONB功能，可以进行高效的规则查询：

```sql
-- 查询包含特定数据源的规则
SELECT * FROM zj_cep_rules 
WHERE rules @> '[{"source": ["auth_log"]}]';

-- 查询包含正则表达式匹配的规则
SELECT * FROM zj_cep_rules 
WHERE rules @> '[{"rules": [{"condition": [{"match_type": "regex"}]}]}]';

-- 查询规则组数量
SELECT id, jsonb_array_length(rules) as rule_group_count 
FROM zj_cep_rules;

-- 提取所有使用的数据源类型
SELECT DISTINCT jsonb_array_elements_text(
    jsonb_path_query_array(rules, '$[*].source[*]')
) as data_sources FROM zj_cep_rules;

-- 查询时间窗口大于60秒的规则
SELECT * FROM zj_cep_rules 
WHERE (within->>'time')::int > 60;

-- 查询启用了可选规则的配置
SELECT * FROM zj_cep_rules 
WHERE rules @> '[{"rules": [{"optional": true}]}]';
```

## 性能优化建议

### 数据库索引

```sql
-- 为JSONB字段创建GIN索引
CREATE INDEX idx_zj_cep_rules_rules_gin ON zj_cep_rules USING GIN (rules);
CREATE INDEX idx_zj_cep_rules_within_gin ON zj_cep_rules USING GIN (within);

-- 为常用查询字段创建B-tree索引
CREATE INDEX idx_zj_cep_rules_status ON zj_cep_rules(status);
CREATE INDEX idx_zj_cep_rules_group_key ON zj_cep_rules(group_key);
CREATE INDEX idx_zj_cep_alert_events_rule_id ON zj_cep_alert_events(rule_id);
CREATE INDEX idx_zj_cep_alert_events_timestamp ON zj_cep_alert_events(timestamp);
```

### Flink配置建议

- **并行度设置**：根据数据量调整并行度
- **检查点配置**：启用检查点确保故障恢复
- **内存配置**：为CEP操作分配足够内存
- **背压监控**：监控处理能力与数据输入速率

## 故障排查

### 常见问题

1. **规则未找到**：检查rule.id参数和数据库中的配置
2. **数据源连接失败**：
   - NATS：验证NATS服务器地址和认证信息
   - Kafka：检查bootstrap.servers地址和网络连通性
3. **数据库连接问题**：确认PostgreSQL连接参数
4. **JSON解析错误**：验证rules和within字段的JSON格式
5. **CEP匹配失败**：检查条件表达式和时间窗口配置
6. **Kafka消费问题**：
   - 检查消费者组ID是否正确
   - 验证主题是否存在
   - 确认偏移量重置策略设置

### 调试方法

- 查看控制台输出的配置信息
- 检查Flink Web UI中的作业状态
- 查看数据库中的告警记录
- 验证NATS消息格式是否正确

## 扩展说明

系统设计支持以下扩展：

1. **新增匹配类型**：在ConditionExpressionEvaluator中添加新的匹配逻辑
2. **自定义数据源**：实现新的Source连接器
3. **告警输出扩展**：添加新的Sink实现（如Kafka、ElasticSearch等）
4. **规则配置界面**：开发Web界面进行规则管理
5. **监控告警**：集成监控系统进行作业健康检查

## 总结

DynamicCEPJob提供了一个强大而灵活的实时复杂事件处理解决方案，支持动态规则配置、复杂条件表达式、多数据源处理等特性。通过支持NATS和Kafka两种主流消息队列，系统具备了更好的适应性和扩展性。JSONB存储格式既保证了性能又提供了查询的灵活性，是企业级实时数据处理的理想选择。

### 数据源选择建议

- **选择NATS**：适合高频小消息、低延迟要求的场景
- **选择Kafka**：适合大规模数据流、需要持久化和回放的场景

无论选择哪种消息队列，系统都提供了统一的配置和使用方式，便于在不同环境中部署和使用。 