# DynamicCEPJob数据流时序图

```mermaid
sequenceDiagram
    participant DMP as DMP<br/>规则管理平台
    participant DB as PostgreSQL<br/>配置数据库
    participant XMSG as X-MSG<br/>消息队列
    participant CLIENT as XMSG Client
    participant J<PERSON> as Flink JobManager
    participant TM as Flink TaskManager
    participant NATS as 数据源<br/>(当前NATS，kafka，后续扩展其他)
    participant PG as 告警输出<br/>(当前PostgreSQL，后续扩展其他)
    
    Note over DMP,PG: 1. 规则配置阶段
    DMP->>DB: 保存规则配置
    DMP->>XMSG: 发送启动任务消息
    
    Note over DMP,PG: 2. 任务启动阶段  
    CLIENT->>XMSG: 拉取任务消息
    CLIENT->>JM: 调用Flink CLI启动作业
    JM->>TM: 分发CEP任务
    TM->>DB: 加载规则配置
    
    Note over DMP,PG: 3. 数据处理阶段
    loop 实时数据处理
        NATS->>TM: 发送日志事件
        TM->>TM: CEP模式匹配
        alt 匹配成功
            TM->>PG: 输出告警结果
        end
    end
    
    Note over DMP,PG: 4. 状态反馈阶段
    TM->>JM: 上报任务状态
    CLIENT->>XMSG: 反馈执行状态
    XMSG->>DMP: 通知任务状态变化
```

## 时序说明

### 阶段1：规则配置
- DMP通过Web界面接收用户配置的CEP规则
- 规则以JSONB格式存储到PostgreSQL数据库
- DMP发送任务启动消息到X-MSG队列

### 阶段2：任务启动
- XMSG Client从消息队列拉取任务管理消息
- Client调用Flink CLI命令启动对应的CEP作业
- JobManager将任务分发到TaskManager执行
- TaskManager从数据库加载规则配置

### 阶段3：数据处理
- NATS持续发送日志事件到TaskManager
- TaskManager执行CEP模式匹配逻辑
- 匹配成功时生成告警并输出到PostgreSQL

### 阶段4：状态反馈
- TaskManager向JobManager上报任务运行状态
- XMSG Client收集状态信息反馈给消息队列
- DMP通过消息队列获取任务状态更新 