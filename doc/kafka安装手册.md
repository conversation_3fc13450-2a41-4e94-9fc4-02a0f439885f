# 部署手册
参考文档：https://kafka.apache.org/quickstart

## 1. 安装jdk
jdk17及以上

## 2. 下载flink
下载地址：https://www.apache.org/dyn/closer.cgi?path=/kafka/4.0.0/kafka_2.13-4.0.0.tgz
国内用这个更快：https://mirrors.aliyun.com/apache/kafka/4.0.0/?spm=a2c6h.25603864.0.0.4fe7126emTcG6I

## 3. 配置初始化
KAFKA_CLUSTER_ID="$(bin/kafka-storage.sh random-uuid)"
bin/kafka-storage.sh format --standalone -t $KAFKA_CLUSTER_ID -c config/server.properties
bin/kafka-server-start.sh config/server.properties

## 4. 修改配置文件config/server.properties
advertised.listeners=PLAINTEXT://192.168.188.188:9092,CONTROLLER://192.168.188.188:9093

## 5. 启动服务
$ bin/kafka-storage.sh format --standalone -t $KAFKA_CLUSTER_ID -c config/server.properties

## 6. 创建主题
kafka-topics.sh --bootstrap-server 192.168.188.188:9092  --create  --topic log_events  --partitions 3  --replication-factor 1
