# 部署手册
参考文档：https://nightlies.apache.org/flink/flink-docs-release-2.0/zh/docs/try-flink/local_installation/

## 1. 安装jdk
jdk17及以上

## 2. 下载flink
下载地址：https://flink.apache.org/zh/downloads/
国内用这个更快：https://mirrors.aliyun.com/apache/flink/

## 3. 启动flink服务
./bin/start-cluster.sh

## 4. 打包应用程序
使用mvn命令打包即可
mvn clean package

## 5. 发布应用程序
./bin/flink run -c org.example.DynamicCEPJob -d /root/zj/flink/flink/job/flink_demo-1.0-SNAPSHOT.jar --rule.id "tunnel_repeated_operations" --source.type "kafka" --kafka.bootstrap.servers 192.168.188.188:9092 --kafka.topic log_events --kafka.group.id flink_consumer_group --kafka.auto.offset.reset latest --db.url "***************************************************************************************************************" --db.user admin --db.password "JAY9^yyds#68"

## 6. 停止任务
### 先获取job的id
./bin/flink list


## 7. 停止任务
### 直接停止，会丢掉状态数据
./bin/flink cancel $JOB_ID

### 优雅停止，可以存储执行进度，下次可以继续
./bin/flink stop --savepointPath /tmp/flink-savepoints  $JOB_ID

## 8. 停止flink服务
./bin/stop-cluster.sh