-- 测试规则设置脚本
-- 为DynamicCEPJob测试场景创建必要的CEP规则

-- 删除可能存在的旧规则
DELETE FROM zj_cep_rules WHERE id IN ('tunnel_repeated_operations', 'auth_success_tunnel_fail');

-- 规则1: 反复隧道连接检测
-- 检测用户在1分钟内进行 OPEN -> CLOSE -> OPEN 的反复操作
INSERT INTO zj_cep_rules (id, group_key, rules, within, status) 
VALUES (
    'tunnel_repeated_operations',
    'user_id',
    '[
      {
        "id": "tunnel_operation_group",
        "source": ["tunnel_log"],
        "rules": [
          {
            "id": "tunnel_open",
            "times": [1, 1],
            "greedy": false,
            "optional": false,
            "one_or_more": false,
            "follow": true,
            "not": false,
            "condition": [
              {
                "id": "1",
                "type": "match",
                "match_key": "res",
                "match_type": "=",
                "match_val": "OPEN"
              }
            ],
            "condition_expression": "1"
          },
          {
            "id": "tunnel_close",
            "times": [1, 1],
            "greedy": false,
            "optional": false,
            "one_or_more": false,
            "follow": true,
            "not": false,
            "condition": [
              {
                "id": "1",
                "type": "match",
                "match_key": "res",
                "match_type": "=",
                "match_val": "CLOSE"
              }
            ],
            "condition_expression": "1"
          },
          {
            "id": "tunnel_open_again",
            "times": [1],
            "greedy": false,
            "optional": false,
            "one_or_more": false,
            "follow": true,
            "not": false,
            "condition": [
              {
                "id": "1",
                "type": "match",
                "match_key": "res",
                "match_type": "=",
                "match_val": "OPEN"
              }
            ],
            "condition_expression": "1"
          }
        ]
      }
    ]'::jsonb,
    '{"time": 60, "unit": "s"}'::jsonb,
    1
);

-- 规则2: 认证成功但连接失败检测
-- 检测用户认证成功后10秒内没有建立隧道连接
INSERT INTO zj_cep_rules (id, group_key, rules, within, status) 
VALUES (
    'auth_success_tunnel_fail',
    'user_id',
    '[
      {
        "id": "auth_success_group",
        "source": ["auth_log"],
        "rules": [
          {
            "id": "auth_success_event",
            "times": [1, 1],
            "greedy": false,
            "optional": false,
            "one_or_more": false,
            "follow": true,
            "not": false,
            "condition": [
              {
                "id": "1",
                "type": "match",
                "match_key": "res",
                "match_type": "=",
                "match_val": "AUTH_SUCCESS"
              }
            ],
            "condition_expression": "1"
          }
        ]
      },
      {
        "id": "tunnel_absence_group",
        "source": ["tunnel_log"],
        "rules": [
          {
            "id": "no_tunnel_open",
            "times": [1, 1],
            "greedy": false,
            "optional": false,
            "one_or_more": false,
            "follow": true,
            "not": true,
            "condition": [
              {
                "id": "1",
                "type": "match",
                "match_key": "res",
                "match_type": "=",
                "match_val": "OPEN"
              }
            ],
            "condition_expression": "1"
          }
        ]
      },
      {
        "id": "auth_success_again_group",
        "source": ["auth_log"],
        "rules": [
          {
            "id": "auth_success_again_event",
            "times": [1, 1],
            "greedy": false,
            "optional": false,
            "one_or_more": false,
            "follow": true,
            "not": false,
            "condition": [
              {
                "id": "1",
                "type": "match",
                "match_key": "res",
                "match_type": "=",
                "match_val": "AUTH_SUCCESS"
              }
            ],
            "condition_expression": "1"
          }
        ]
      }
    ]'::jsonb,
    '{"time": 10, "unit": "s"}'::jsonb,
    1
);

-- 验证规则是否正确插入
SELECT 
    id,
    group_key,
    jsonb_pretty(rules) as rules_formatted,
    jsonb_pretty(within) as within_formatted,
    status,
    add_time
FROM zj_cep_rules 
WHERE id IN ('tunnel_repeated_operations', 'auth_success_tunnel_fail')
ORDER BY id;

-- 显示规则统计信息
SELECT 
    '规则创建完成' as message,
    COUNT(*) as total_rules,
    COUNT(CASE WHEN status = 1 THEN 1 END) as active_rules
FROM zj_cep_rules 
WHERE id IN ('tunnel_repeated_operations', 'auth_success_tunnel_fail');

-- 清理旧的告警记录（可选）
-- TRUNCATE TABLE zj_cep_alerts;

COMMIT; 