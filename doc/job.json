{"jid": "92d820bedf5501220711f55c470979cf", "name": "Dynamic CEP Job - tunnel_repeated_operations", "isStoppable": false, "state": "RUNNING", "job-type": "STREAMING", "start-time": 1752140443810, "end-time": -1, "duration": 1543748, "maxParallelism": -1, "now": 1752141987558, "timestamps": {"CANCELLING": 0, "INITIALIZING": 1752140443810, "FAILING": 0, "FINISHED": 0, "CANCELED": 0, "CREATED": 1752140447898, "SUSPENDED": 0, "RECONCILING": 0, "RUNNING": 1752140448012, "RESTARTING": 0, "FAILED": 0}, "vertices": [{"id": "bc764cd8ddf7a0cff126f51c16239658", "slotSharingGroupId": "8cb6c774d14072ce282e32813821a437", "name": "Source: Kafka Log Source", "maxParallelism": 128, "parallelism": 1, "status": "RUNNING", "start-time": 1752140448277, "end-time": -1, "duration": 1539281, "tasks": {"CANCELING": 0, "FAILED": 0, "FINISHED": 0, "RUNNING": 1, "DEPLOYING": 0, "SCHEDULED": 0, "CANCELED": 0, "INITIALIZING": 0, "RECONCILING": 0, "CREATED": 0}, "metrics": {"read-bytes": 0, "read-bytes-complete": false, "write-bytes": 0, "write-bytes-complete": false, "read-records": 0, "read-records-complete": false, "write-records": 0, "write-records-complete": false, "accumulated-backpressured-time": 0, "accumulated-idle-time": 0, "accumulated-busy-time": 0.0}}, {"id": "0a448493b4782967b150582570326227", "slotSharingGroupId": "8cb6c774d14072ce282e32813821a437", "name": "Timestamps/Watermarks", "maxParallelism": 128, "parallelism": 1, "status": "RUNNING", "start-time": 1752140448281, "end-time": -1, "duration": 1539277, "tasks": {"CANCELING": 0, "FAILED": 0, "FINISHED": 0, "RUNNING": 1, "DEPLOYING": 0, "SCHEDULED": 0, "CANCELED": 0, "INITIALIZING": 0, "RECONCILING": 0, "CREATED": 0}, "metrics": {"read-bytes": 0, "read-bytes-complete": false, "write-bytes": 0, "write-bytes-complete": false, "read-records": 0, "read-records-complete": false, "write-records": 0, "write-records-complete": false, "accumulated-backpressured-time": 0, "accumulated-idle-time": 0, "accumulated-busy-time": 0.0}}, {"id": "e70bbd798b564e0a50e10e343f1ac56b", "slotSharingGroupId": "8cb6c774d14072ce282e32813821a437", "name": "CepOperator -> Filter -> (Sink: Writer, Sink: Print to Std. Out)", "maxParallelism": 128, "parallelism": 1, "status": "RUNNING", "start-time": 1752140448298, "end-time": -1, "duration": 1539260, "tasks": {"CANCELING": 0, "FAILED": 0, "FINISHED": 0, "RUNNING": 1, "DEPLOYING": 0, "SCHEDULED": 0, "CANCELED": 0, "INITIALIZING": 0, "RECONCILING": 0, "CREATED": 0}, "metrics": {"read-bytes": 0, "read-bytes-complete": false, "write-bytes": 0, "write-bytes-complete": false, "read-records": 0, "read-records-complete": false, "write-records": 0, "write-records-complete": false, "accumulated-backpressured-time": 0, "accumulated-idle-time": 0, "accumulated-busy-time": 0.0}}], "status-counts": {"CANCELING": 0, "FAILED": 0, "FINISHED": 0, "RUNNING": 3, "DEPLOYING": 0, "SCHEDULED": 0, "CANCELED": 0, "INITIALIZING": 0, "RECONCILING": 0, "CREATED": 0}, "plan": {"jid": "92d820bedf5501220711f55c470979cf", "name": "Dynamic CEP Job - tunnel_repeated_operations", "type": "STREAMING", "nodes": [{"id": "e70bbd798b564e0a50e10e343f1ac56b", "parallelism": 1, "operator": "", "operator_strategy": "", "description": "CepOperator<br/>+- Filter<br/>   :- <PERSON><PERSON>: Writer<br/>   +- Sink: Print to Std. Out<br/>", "inputs": [{"num": 0, "id": "0a448493b4782967b150582570326227", "ship_strategy": "HASH", "exchange": "pipelined_bounded"}], "optimizer_properties": {}}, {"id": "0a448493b4782967b150582570326227", "parallelism": 1, "operator": "", "operator_strategy": "", "description": "Timestamps/Watermarks<br/>", "inputs": [{"num": 0, "id": "bc764cd8ddf7a0cff126f51c16239658", "ship_strategy": "HASH", "exchange": "pipelined_bounded"}], "optimizer_properties": {}}, {"id": "bc764cd8ddf7a0cff126f51c16239658", "parallelism": 1, "operator": "", "operator_strategy": "", "description": "Source: Kafka Log Source<br/>", "optimizer_properties": {}}]}, "pending-operators": 0}