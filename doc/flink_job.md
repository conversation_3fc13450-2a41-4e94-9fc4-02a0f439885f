# Flink作业信息字段说明

## 数据获取方式
此JSON数据通过调用Flink REST API接口获取：
```
GET localhost:8082/jobs/{job-id}
```
例如：`localhost:8082/jobs/92d820bedf5501220711f55c470979cf`

## 完整JSON数据示例
```json
{
  "jid": "92d820bedf5501220711f55c470979cf",
  "name": "Dynamic CEP Job - tunnel_repeated_operations",
  "isStoppable": false,
  "state": "RUNNING",
  "job-type": "STREAMING",
  "start-time": 1752140443810,
  "end-time": -1,
  "duration": 1543748,
  "maxParallelism": -1,
  "now": 1752141987558,
  "timestamps": {
    "CANCELLING": 0,
    "INITIALIZING": 1752140443810,
    "FAILING": 0,
    "FINISHED": 0,
    "CANCELED": 0,
    "CREATED": 1752140447898,
    "SUSPENDED": 0,
    "RECONCILING": 0,
    "RUNNING": 1752140448012,
    "RESTARTING": 0,
    "FAILED": 0
  },
  "vertices": [
    {
      "id": "bc764cd8ddf7a0cff126f51c16239658",
      "slotSharingGroupId": "8cb6c774d14072ce282e32813821a437",
      "name": "Source: Kafka Log Source",
      "maxParallelism": 128,
      "parallelism": 1,
      "status": "RUNNING",
      "start-time": 1752140448277,
      "end-time": -1,
      "duration": 1539281,
      "tasks": {
        "CANCELING": 0,
        "FAILED": 0,
        "FINISHED": 0,
        "RUNNING": 1,
        "DEPLOYING": 0,
        "SCHEDULED": 0,
        "CANCELED": 0,
        "INITIALIZING": 0,
        "RECONCILING": 0,
        "CREATED": 0
      },
      "metrics": {
        "read-bytes": 0,
        "read-bytes-complete": false,
        "write-bytes": 0,
        "write-bytes-complete": false,
        "read-records": 0,
        "read-records-complete": false,
        "write-records": 0,
        "write-records-complete": false,
        "accumulated-backpressured-time": 0,
        "accumulated-idle-time": 0,
        "accumulated-busy-time": 0.0
      }
    }
  ],
  "status-counts": {
    "CANCELING": 0,
    "FAILED": 0,
    "FINISHED": 0,
    "RUNNING": 3,
    "DEPLOYING": 0,
    "SCHEDULED": 0,
    "CANCELED": 0,
    "INITIALIZING": 0,
    "RECONCILING": 0,
    "CREATED": 0
  },
  "plan": {
    "jid": "92d820bedf5501220711f55c470979cf",
    "name": "Dynamic CEP Job - tunnel_repeated_operations",
    "type": "STREAMING",
    "nodes": [
      {
        "id": "e70bbd798b564e0a50e10e343f1ac56b",
        "parallelism": 1,
        "operator": "",
        "operator_strategy": "",
        "description": "CepOperator<br/>+- Filter<br/>   :- Sink: Writer<br/>   +- Sink: Print to Std. Out<br/>",
        "inputs": [
          {
            "num": 0,
            "id": "0a448493b4782967b150582570326227",
            "ship_strategy": "HASH",
            "exchange": "pipelined_bounded"
          }
        ],
        "optimizer_properties": {}
      }
    ]
  },
  "pending-operators": 0
}
```

## 顶层字段说明

### 基本信息
```json
{
  "jid": "92d820bedf5501220711f55c470979cf",
  "name": "Dynamic CEP Job - tunnel_repeated_operations",
  "isStoppable": false,
  "state": "RUNNING",
  "job-type": "STREAMING"
}
```

- **jid**: 作业ID，Flink为每个作业生成的唯一标识符
- **name**: 作业名称，用户定义的作业名称
- **isStoppable**: 作业是否可停止，布尔值表示作业是否支持优雅停止
- **state**: 作业当前状态，可能的值包括：RUNNING（运行中）、FINISHED（已完成）、FAILED（失败）、CANCELED（已取消）等
- **job-type**: 作业类型，通常为"STREAMING"（流处理）或"BATCH"（批处理）

### 时间信息
```json
{
  "start-time": 1752140443810,
  "end-time": -1,
  "duration": 1543748,
  "now": 1752141987558
}
```

- **start-time**: 作业开始时间，Unix时间戳（毫秒）
- **end-time**: 作业结束时间，Unix时间戳（毫秒），-1表示作业仍在运行
- **duration**: 作业运行持续时间，毫秒
- **now**: API调用时的当前时间戳，毫秒

### 并行度配置
```json
{
  "maxParallelism": -1
}
```

- **maxParallelism**: 最大并行度，-1表示使用默认值

## timestamps 对象
```json
{
  "timestamps": {
    "CANCELLING": 0,
    "INITIALIZING": 1752140443810,
    "FAILING": 0,
    "FINISHED": 0,
    "CANCELED": 0,
    "CREATED": 1752140447898,
    "SUSPENDED": 0,
    "RECONCILING": 0,
    "RUNNING": 1752140448012,
    "RESTARTING": 0,
    "FAILED": 0
  }
}
```

记录作业在各个状态下的时间戳，包括：
- **INITIALIZING**: 初始化时间
- **CREATED**: 创建时间
- **RUNNING**: 开始运行时间
- **FINISHED**: 完成时间
- **FAILED**: 失败时间
- **CANCELED**: 取消时间
- **CANCELLING**: 正在取消时间
- **SUSPENDED**: 暂停时间
- **RESTARTING**: 重启时间
- **RECONCILING**: 调和时间
- **FAILING**: 正在失败时间

值为0表示该状态未发生过。

## vertices 数组
```json
{
  "vertices": [
    {
      "id": "bc764cd8ddf7a0cff126f51c16239658",
      "slotSharingGroupId": "8cb6c774d14072ce282e32813821a437",
      "name": "Source: Kafka Log Source",
      "maxParallelism": 128,
      "parallelism": 1,
      "status": "RUNNING",
      "start-time": 1752140448277,
      "end-time": -1,
      "duration": 1539281,
      "tasks": {
        "CANCELING": 0,
        "FAILED": 0,
        "FINISHED": 0,
        "RUNNING": 1,
        "DEPLOYING": 0,
        "SCHEDULED": 0,
        "CANCELED": 0,
        "INITIALIZING": 0,
        "RECONCILING": 0,
        "CREATED": 0
      },
      "metrics": {
        "read-bytes": 0,
        "read-bytes-complete": false,
        "write-bytes": 0,
        "write-bytes-complete": false,
        "read-records": 0,
        "read-records-complete": false,
        "write-records": 0,
        "write-records-complete": false,
        "accumulated-backpressured-time": 0,
        "accumulated-idle-time": 0,
        "accumulated-busy-time": 0.0
      }
    }
  ]
}
```

作业图中的顶点（算子）信息，每个顶点包含：

### 基本信息
- **id**: 顶点唯一标识符
- **slotSharingGroupId**: 插槽共享组ID，相同组内的算子可以共享TaskManager插槽
- **name**: 顶点名称，描述算子功能
- **parallelism**: 当前并行度
- **maxParallelism**: 最大并行度
- **status**: 顶点当前状态

### 时间信息
- **start-time**: 顶点开始时间
- **end-time**: 顶点结束时间
- **duration**: 顶点运行持续时间

### tasks 对象
统计该顶点下各种状态的任务数量：
- **RUNNING**: 正在运行的任务数
- **FINISHED**: 已完成的任务数
- **FAILED**: 失败的任务数
- **CANCELED**: 已取消的任务数
- **DEPLOYING**: 正在部署的任务数
- **SCHEDULED**: 已调度的任务数
- **INITIALIZING**: 正在初始化的任务数
- **RECONCILING**: 正在调和的任务数
- **CREATED**: 已创建的任务数
- **CANCELING**: 正在取消的任务数

### metrics 对象
顶点的性能指标：
- **read-bytes**: 读取字节数
- **read-bytes-complete**: 读取字节数统计是否完整
- **write-bytes**: 写入字节数
- **write-bytes-complete**: 写入字节数统计是否完整
- **read-records**: 读取记录数
- **read-records-complete**: 读取记录数统计是否完整
- **write-records**: 写入记录数
- **write-records-complete**: 写入记录数统计是否完整
- **accumulated-backpressured-time**: 累积背压时间
- **accumulated-idle-time**: 累积空闲时间
- **accumulated-busy-time**: 累积繁忙时间

## status-counts 对象
```json
{
  "status-counts": {
    "CANCELING": 0,
    "FAILED": 0,
    "FINISHED": 0,
    "RUNNING": 3,
    "DEPLOYING": 0,
    "SCHEDULED": 0,
    "CANCELED": 0,
    "INITIALIZING": 0,
    "RECONCILING": 0,
    "CREATED": 0
  }
}
```

整个作业中各种状态的顶点数量统计，字段含义与vertices中的tasks对象相同。

## plan 对象
```json
{
  "plan": {
    "jid": "92d820bedf5501220711f55c470979cf",
    "name": "Dynamic CEP Job - tunnel_repeated_operations",
    "type": "STREAMING",
    "nodes": [
      {
        "id": "e70bbd798b564e0a50e10e343f1ac56b",
        "parallelism": 1,
        "operator": "",
        "operator_strategy": "",
        "description": "CepOperator<br/>+- Filter<br/>   :- Sink: Writer<br/>   +- Sink: Print to Std. Out<br/>",
        "inputs": [
          {
            "num": 0,
            "id": "0a448493b4782967b150582570326227",
            "ship_strategy": "HASH",
            "exchange": "pipelined_bounded"
          }
        ],
        "optimizer_properties": {}
      },
      {
        "id": "0a448493b4782967b150582570326227",
        "parallelism": 1,
        "operator": "",
        "operator_strategy": "",
        "description": "Timestamps/Watermarks<br/>",
        "inputs": [
          {
            "num": 0,
            "id": "bc764cd8ddf7a0cff126f51c16239658",
            "ship_strategy": "HASH",
            "exchange": "pipelined_bounded"
          }
        ],
        "optimizer_properties": {}
      },
      {
        "id": "bc764cd8ddf7a0cff126f51c16239658",
        "parallelism": 1,
        "operator": "",
        "operator_strategy": "",
        "description": "Source: Kafka Log Source<br/>",
        "optimizer_properties": {}
      }
    ]
  }
}
```

作业执行计划信息：

### 基本信息
- **jid**: 作业ID
- **name**: 作业名称
- **type**: 作业类型

### nodes 数组
执行计划中的节点信息：
- **id**: 节点ID，对应vertices中的顶点ID
- **parallelism**: 并行度
- **operator**: 算子信息
- **operator_strategy**: 算子策略
- **description**: 节点描述，包含算子的详细信息

#### inputs 数组（如果存在）
节点的输入连接信息：
- **num**: 输入编号
- **id**: 输入来源节点ID
- **ship_strategy**: 数据传输策略（如HASH、REBALANCE等）
- **exchange**: 数据交换类型（如pipelined_bounded）

### optimizer_properties 对象
优化器属性，通常为空对象。

## 其他字段
```json
{
  "pending-operators": 0
}
```

- **pending-operators**: 待处理的算子数量

## 示例作业分析
根据提供的JSON，该作业"Dynamic CEP Job - tunnel_repeated_operations"包含三个主要组件：
1. **Kafka Log Source**: 从Kafka读取日志数据
2. **Timestamps/Watermarks**: 添加时间戳和水印
3. **CepOperator**: CEP模式匹配，包含过滤器和两个输出端（Writer和标准输出）

作业当前处于RUNNING状态，所有组件都正常运行。 