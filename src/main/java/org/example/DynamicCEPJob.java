package org.example;

import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.cep.CEP;
import org.apache.flink.cep.PatternSelectFunction;
import org.apache.flink.cep.PatternStream;
import org.apache.flink.cep.nfa.aftermatch.AfterMatchSkipStrategy;
import org.apache.flink.cep.pattern.Pattern;
import org.apache.flink.cep.pattern.conditions.SimpleCondition;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.configuration.StateBackendOptions;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.shaded.curator5.com.google.common.base.Strings;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.util.ParameterTool;
import org.example.config.NewCEPRuleConfig;
import org.example.config.NewSubRule;
import org.example.config.NewSubRuleGroup;
import org.example.loader.NewRuleConfigLoader;
import org.example.model.AlertEvent;
import org.example.model.LogEvent;
import org.example.processor.ConditionExpressionEvaluator;
import org.example.sink.AlertEventPostgreSQLSink;
import org.example.source.NatsLogEventSource;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

/**
 * 动态CEP作业，从PostgreSQL加载规则配置，从NATS获取数据
 * 支持新的规则配置结构，在单一stream上处理所有规则组
 * 使用数据库表的group_key字段进行keyBy，使用within字段设置CEP时间窗口
 */
public class DynamicCEPJob {

    public static void main(String[] args) throws Exception {
        // 获取参数
        ParameterTool params = ParameterTool.fromArgs(args);

        // 必要参数检查
        if (!params.has("rule.id")) {
            throw new IllegalArgumentException("必须提供rule.id参数");
        }

        String ruleId = params.get("rule.id");
        String dbUrl = params.get("db.url", "***************************************************************************************************************");
        String dbUser = params.get("db.user", "admin");
        String dbPassword = params.get("db.password", "JAY9^yyds#68");
        // 数据源类型配置 (nats 或 kafka)
        String sourceType = params.get("source.type", "nats");

        // NATS 相关配置
        String natsUrl = params.get("nats.url", "nats://192.168.188.188:4242");
        String natsSubject = params.get("nats.subject", "log_events");
        String natsUsername = params.get("nats.username", "dialer_abc");
        String natsPassword = params.get("nats.password", "efgh1234");
        String natsGroup = params.get("nats.group", "flink_consumer"); // NATS消费者组配置

        // Kafka 相关配置
        String kafkaBootstrapServers = params.get("kafka.bootstrap.servers", "192.168.188.188:9092");
        String kafkaTopic = params.get("kafka.topic", "log_events");
        String kafkaGroupId = params.get("kafka.group.id", "flink_consumer_group");
        String kafkaOffsetReset = params.get("kafka.auto.offset.reset", "latest"); // earliest, latest


        // 设置执行环境
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        Configuration flinkConfiguration = new Configuration();
        flinkConfiguration.set(StateBackendOptions.STATE_BACKEND, "hashmap");
        env.configure(flinkConfiguration);
        // 🔧 强制设置并行度为1，确保CEP正常工作
        // 这是临时解决方案，用于调试Kafka数据源问题
        System.out.println("🔧 强制设置并行度为1，确保CEP正常工作");

        // 生产环境设置：允许并行处理，但依赖keyBy确保相关事件在同一任务实例
        // 开发环境可以设置 env.setParallelism(1); 进行测试
        String parallelismStr = params.get("parallelism", "auto");
        if (!"auto".equals(parallelismStr)) {
            int parallelism = Integer.parseInt(parallelismStr);
            env.setParallelism(parallelism);
            System.out.println("设置并行度: " + parallelism);
        } else {
            System.out.println("使用默认并行度: " + env.getParallelism());
        }

        // 从PostgreSQL加载规则配置
        NewRuleConfigLoader ruleLoader = new NewRuleConfigLoader(dbUrl, dbUser, dbPassword);
        NewCEPRuleConfig ruleConfig = ruleLoader.loadRuleConfig(ruleId);

        if (ruleConfig == null) {
            throw new IllegalArgumentException("未找到ID为 " + ruleId + " 的规则配置");
        }

        if (ruleConfig.rules == null || ruleConfig.rules.isEmpty()) {
            throw new IllegalArgumentException("规则配置为空");
        }

        if (ruleConfig.within == null) {
            throw new IllegalArgumentException("未配置时间窗口(within字段)");
        }

        // 校验最后一条规则的not属性
        validateLastRuleNotProperty(ruleConfig.rules);

        // 输出配置信息
        System.out.println("加载规则配置: " + ruleConfig);
        System.out.println("使用数据源类型: " + sourceType);

        // 根据数据源类型创建数据流
        DataStream<LogEvent> logStream;

        if ("kafka".equalsIgnoreCase(sourceType)) {
            // 从Kafka获取数据流
            System.out.println("使用Kafka数据源 - 服务器: " + kafkaBootstrapServers + ", 主题: " + kafkaTopic + ", 消费组: " + kafkaGroupId);

            // 根据参数设置偏移量重置策略
            OffsetsInitializer offsetsInitializer = "latest".equalsIgnoreCase(kafkaOffsetReset) ?
                    OffsetsInitializer.latest() : OffsetsInitializer.earliest();

            KafkaSource<LogEvent> kafkaSource = KafkaSource.<LogEvent>builder()
                    .setBootstrapServers(kafkaBootstrapServers)
                    .setTopics(kafkaTopic)
                    .setGroupId(kafkaGroupId)
                    .setStartingOffsets(offsetsInitializer)
                    .setValueOnlyDeserializer(new LogEventDeserializationSchema())
                    .setProperty("request.timeout.ms", "60000")
                    .setProperty("session.timeout.ms", "30000")
                    .setProperty("heartbeat.interval.ms", "10000")
                    .setProperty("max.poll.interval.ms", "300000")
                    .setProperty("connections.max.idle.ms", "540000")
                    .setProperty("metadata.max.age.ms", "300000")
                    .build();

            logStream = env.fromSource(
                    kafkaSource,
                    WatermarkStrategy.<LogEvent>forBoundedOutOfOrderness(Duration.ofSeconds(5))
                            .withTimestampAssigner((event, timestamp) ->
                                    event.timestamp.toInstant().toEpochMilli()),
                    "Kafka Log Source"
            ).setParallelism(1).keyBy(event -> {
                String keyValue = event.getFieldValue(ruleConfig.groupKey);
                String finalKey = (keyValue != null) ? keyValue : "unknown";
                System.out.println("   🔑 事件分组: " + finalKey + " -> " + event.getFieldValue("res"));
                return finalKey;
            });
        } else {
            // 从NATS获取数据流 (默认)
            System.out.println("使用NATS数据源 - 服务器: " + natsUrl + ", 主题: " + natsSubject + ", 消费组: " + natsGroup);

            NatsLogEventSource natsSource;
            if (natsUsername != null && natsPassword != null) {
                natsSource = new NatsLogEventSource(natsUrl, natsSubject, natsUsername, natsPassword, natsGroup);
            } else {
                natsSource = new NatsLogEventSource(natsUrl, natsSubject, natsGroup);
            }

            logStream = env.fromSource(
                    natsSource,
                    WatermarkStrategy.<LogEvent>forBoundedOutOfOrderness(Duration.ofSeconds(5))
                            .withTimestampAssigner((event, timestamp) ->
                                    event.timestamp.toInstant().toEpochMilli()),
                    "NATS Log Source"
            ).setParallelism(1).keyBy(event -> {
                String keyValue = event.getFieldValue(ruleConfig.groupKey);
                String finalKey = (keyValue != null) ? keyValue : "unknown";
                System.out.println("   🔑 事件分组: " + finalKey + " -> " + event.getFieldValue("res"));
                return finalKey;
            });
        }

        // 使用数据库表的group_key字段进行keyBy
        System.out.println("🔑 配置的分组键: " + ruleConfig.groupKey);
        System.out.println("   当前并行度: " + env.getParallelism());

        DataStream<LogEvent> keyedStream;
        if (Strings.isNullOrEmpty(ruleConfig.groupKey)) {
            if (env.getParallelism() > 1) {
                System.out.println("⚠️  警告：未配置分组键但并行度>1，可能导致CEP匹配失败");
                System.out.println("   建议：配置group_key或设置并行度为1");
            }
        }

        System.out.println("   使用分组键进行keyBy: " + ruleConfig.groupKey);
        System.out.println("   这将确保相同" + ruleConfig.groupKey + "的事件在同一任务实例中处理");

        keyedStream = logStream
                .assignTimestampsAndWatermarks(
                        WatermarkStrategy
                                .<LogEvent>forBoundedOutOfOrderness(Duration.ofSeconds(5))
                                .withTimestampAssigner((person, ts) -> person.timestamp.toInstant().toEpochMilli())
                ).keyBy(event -> {
                    String keyValue = event.getFieldValue(ruleConfig.groupKey);
                    String finalKey = (keyValue != null) ? keyValue : "unknown";
                    System.out.println("   🔑 事件分组: " + finalKey + " -> " + event.getFieldValue("res"));
                    return finalKey;
                });

        // 构建合并的Pattern，将所有规则组的规则合并

        Pattern<LogEvent, ?> combinedPattern = buildCombinedPattern(ruleConfig.rules, ruleConfig.within);

        if (combinedPattern == null) {
            System.out.println("❌ Pattern构建失败：没有有效的规则");
            throw new IllegalArgumentException("没有有效的规则");
        }

        PatternStream<LogEvent> patternStream = CEP.pattern(keyedStream, combinedPattern).inEventTime();

        // 处理匹配结果和超时
        DataStream<AlertEvent> alertStream = patternStream
                .select(
                        // 成功匹配处理
                        new PatternSelectFunction<LogEvent, AlertEvent>() {
                            @Override
                            public AlertEvent select(Map<String, List<LogEvent>> matchedPattern) {
                                System.out.println("🎉 CEP 模式匹配成功！匹配的步骤: " + matchedPattern.keySet());

                                // 获取第一个匹配的事件作为基础信息
                                LogEvent firstEvent = null;
                                for (List<LogEvent> events : matchedPattern.values()) {
                                    if (!events.isEmpty()) {
                                        firstEvent = events.get(0);
                                        break;
                                    }
                                }

                                if (firstEvent != null) {
                                    String groupKeyValue = Strings.isNullOrEmpty(ruleConfig.groupKey) ?
                                            "global" : firstEvent.getFieldValue(ruleConfig.groupKey);

                                    // 计算匹配事件总数
                                    int totalMatchedEvents = matchedPattern.values().stream()
                                            .mapToInt(List::size)
                                            .sum();

                                    String alertMessage = String.format(
                                            "规则配置 %s 完全匹配成功，分组键: %s，分组值: %s，时间窗口: %s，匹配步骤: %s，总匹配事件数: %d",
                                            ruleId,
                                            ruleConfig.groupKey,
                                            groupKeyValue,
                                            ruleConfig.within,
                                            matchedPattern.keySet(),
                                            totalMatchedEvents
                                    );

                                    return new AlertEvent(
                                            groupKeyValue,
                                            "COMBINED_RULES_MATCHED",
                                            alertMessage,
                                            LocalDateTime.now(),
                                            ruleId,  // 规则ID
                                            matchedPattern  // 告警内容(完整的匹配Pattern)
                                    );
                                }
                                return null;
                            }
                        }
                ).filter(alert -> alert != null);

        // 输出到PostgreSQL数据库
        alertStream.sinkTo(
                new AlertEventPostgreSQLSink(dbUrl, dbUser, dbPassword)
        );

        // 输出到控制台
        alertStream.print();

        // 执行作业
        env.execute("Dynamic CEP Job - " + ruleId);
    }


    /**
     * 构建合并的Pattern，将所有规则组的规则按顺序合并
     * 使用数据库配置的within时间窗口
     *
     * @param ruleGroups
     * @param within
     * @return
     */
    private static Pattern<LogEvent, ?> buildCombinedPattern(List<NewSubRuleGroup> ruleGroups,
                                                             org.example.config.WithinConfig within) {
        if (ruleGroups == null || ruleGroups.isEmpty()) {
            System.out.println("⚠️  Pattern构建失败：规则组为空");
            return null;
        }

        System.out.println("🔧 开始构建Pattern，规则组数量: " + ruleGroups.size());

        Pattern<LogEvent, ?> pattern = null;

        // 遍历所有规则组，合并所有规则
        for (NewSubRuleGroup ruleGroup : ruleGroups) {
            if (ruleGroup.rules == null || ruleGroup.rules.isEmpty()) {
                System.out.println("   ⏭ 跳过空规则组: " + ruleGroup.id);
                continue;
            }

            System.out.println("   📋 处理规则组: " + ruleGroup.id + ", 规则数量: " + ruleGroup.rules.size());

            // 遍历当前规则组的所有规则
            for (NewSubRule rule : ruleGroup.rules) {
                String stepName = String.format("step_%s_%s", ruleGroup.id, rule.id);
                System.out.println("     🔗 添加规则步骤: " + stepName);

                // 创建条件，需要同时检查数据源和条件表达式
                SimpleCondition<LogEvent> condition = new SimpleCondition<LogEvent>() {
                    public int callCount = 0;

                    @Override
                    public boolean filter(LogEvent event) {
                        callCount++;
                        System.out.println("🎯 CEP条件检查 [" + stepName + "] (第" + callCount + "次调用): " +
                                "事件=" + event.logType +
                                ", user_id=" + event.getFieldValue("user_id") +
                                ", res=" + event.getFieldValue("res") +
                                ", 时间=" + event.timestamp);
                        System.out.flush(); // 强制刷新输出

                        // 首先检查数据源是否匹配（支持多个数据源）
                        if (ruleGroup.source == null || ruleGroup.source.length == 0) {
                            // 如果没有指定数据源，则匹配所有事件
                            boolean result = ConditionExpressionEvaluator.evaluate(
                                    rule.conditionExpression,
                                    rule.condition,
                                    event
                            );
                            System.out.println("   📋 无数据源限制，条件评估结果: " + result);
                            return result;
                        }

                        // 检查事件的logType是否在规则组的数据源列表中
                        boolean sourceMatched = false;
                        for (String sourceType : ruleGroup.source) {
                            if (sourceType.equals(event.logType)) {
                                sourceMatched = true;
                                break;
                            }
                        }

                        if (!sourceMatched) {
                            System.out.println("   ❌ 数据源不匹配: 事件类型=" + event.logType +
                                    ", 期望类型=" + java.util.Arrays.toString(ruleGroup.source));
                            return false;
                        }

                        // 然后使用条件表达式求值器
                        boolean conditionResult = ConditionExpressionEvaluator.evaluate(
                                rule.conditionExpression,
                                rule.condition,
                                event
                        );

                        System.out.println("   ✅ 数据源匹配，条件评估结果: " + conditionResult);
                        return conditionResult;
                    }
                };

                if (pattern == null) {
                    // 第一个条件，使用begin创建Pattern
                    System.out.println("     🚀 创建第一个Pattern步骤: " + stepName);
                    pattern = Pattern.<LogEvent>begin(stepName, AfterMatchSkipStrategy.skipPastLastEvent()).where(condition);
                } else {
                    // 后续条件，根据follow和not属性选择连接方式
                    if (Boolean.TRUE.equals(rule.not)) {
                        // 使用not模式
                        if (Boolean.TRUE.equals(rule.follow)) {
                            pattern = pattern.notFollowedBy(stepName).where(condition);
                        } else {
                            pattern = pattern.notNext(stepName).where(condition);
                        }
                    } else {
                        // 使用正常模式
                        if (Boolean.TRUE.equals(rule.follow)) {
                            pattern = pattern.followedBy(stepName).where(condition);
                        } else {
                            pattern = pattern.next(stepName).where(condition);
                        }
                    }
                }

                // 应用times配置
                if (rule.times != null && rule.times.length >= 2) {
                    int minTimes = rule.times[0];
                    int maxTimes = rule.times[1];

                    if (minTimes == maxTimes && minTimes > 1) {
                        pattern = pattern.times(minTimes);
                    } else if (minTimes < maxTimes) {
                        pattern = pattern.times(minTimes, maxTimes);
                    }
                }

                // 应用greedy配置
                if (Boolean.TRUE.equals(rule.greedy)) {
                    pattern = pattern.greedy();
                }

                // 应用optional配置
                if (Boolean.TRUE.equals(rule.optional)) {
                    pattern = pattern.optional();
                }

                // 应用oneOrMore配置
                if (Boolean.TRUE.equals(rule.oneOrMore)) {
                    pattern = pattern.oneOrMore();
                }
            }
        }

        // 使用数据库配置的within时间窗口
        if (pattern != null && within != null) {
            pattern = pattern.within(within.toDuration());
        }

        return pattern;
    }

    /**
     * 校验规则配置中最后一条规则的not属性不能为true
     *
     * @param ruleGroups 规则组列表
     * @param ruleGroups
     * @throws IllegalArgumentException 如果最后一条规则的not属性为true
     */
    private static void validateLastRuleNotProperty(List<NewSubRuleGroup> ruleGroups) {
        if (ruleGroups == null || ruleGroups.isEmpty()) {
            return;
        }

        // 找到所有规则组中的最后一条规则
        NewSubRule lastRule = null;
        String lastRuleGroupId = null;

        // 遍历所有规则组，找到整体的最后一条规则
        for (NewSubRuleGroup ruleGroup : ruleGroups) {
            if (ruleGroup.rules != null && !ruleGroup.rules.isEmpty()) {
                // 获取当前规则组的最后一条规则
                NewSubRule currentLastRule = ruleGroup.rules.get(ruleGroup.rules.size() - 1);
                if (lastRule == null) {
                    // 第一次找到规则，直接赋值
                    lastRule = currentLastRule;
                    lastRuleGroupId = ruleGroup.id;
                } else {
                    // 后续找到的规则会覆盖前面的（按顺序处理，最后一个规则组的最后一条规则就是整体的最后一条）
                    lastRule = currentLastRule;
                    lastRuleGroupId = ruleGroup.id;
                }
            }
        }

        // 检查最后一条规则的not属性
        if (lastRule != null && Boolean.TRUE.equals(lastRule.not)) {
            throw new IllegalArgumentException(
                    String.format("规则配置校验失败：最后一条规则（规则组：%s，规则ID：%s）的not属性不能为true。" +
                                    "CEP模式的最后一步应该是正向匹配，而不是否定匹配。",
                            lastRuleGroupId, lastRule.id)
            );
        }

        System.out.println("规则配置校验通过：最后一条规则的not属性校验成功");
    }

    /**
     * 测试 Kafka 连接并检查主题是否存在
     *
     * @param bootstrapServers Kafka 服务器地址
     * @param topicName        主题名称
     * @param bootstrapServers
     * @param topicName
     * @throws Exception 如果连接失败或主题不存在
     * @throws Exception
     */
    private static void testKafkaConnection(String bootstrapServers, String topicName) throws Exception {
        Properties props = new Properties();
        props.put("bootstrap.servers", bootstrapServers);
        props.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        props.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        props.put("group.id", "flink-test-connection");
        props.put("auto.offset.reset", "latest");
        props.put("enable.auto.commit", "false");
        props.put("request.timeout.ms", "3000000");
        props.put("session.timeout.ms", "1000000");

        try (org.apache.kafka.clients.admin.AdminClient adminClient =
                     org.apache.kafka.clients.admin.AdminClient.create(props)) {

            System.out.println("正在测试 Kafka 连接...");

            // 测试连接并获取主题列表
            java.util.Set<String> topicNames = adminClient.listTopics().names().get(10, java.util.concurrent.TimeUnit.SECONDS);

            System.out.println("Kafka 连接成功！可用主题数量: " + topicNames.size());

            if (!topicNames.contains(topicName)) {
                throw new Exception("主题 '" + topicName + "' 不存在。" +
                        "\n可用主题: " + topicNames +
                        "\n请使用以下命令创建主题:" +
                        "\nkafka-topics.sh --bootstrap-server " + bootstrapServers +
                        " --create --topic " + topicName + " --partitions 3 --replication-factor 1");
            }

            // 获取主题详细信息
            var topicDescription = adminClient.describeTopics(java.util.Collections.singleton(topicName))
                    .values().get(topicName).get(10, java.util.concurrent.TimeUnit.SECONDS);

            System.out.println("主题 '" + topicName + "' 验证成功！分区数: " + topicDescription.partitions().size());

        } catch (java.util.concurrent.TimeoutException e) {
            throw new Exception("连接 Kafka 超时，请检查服务器地址: " + bootstrapServers, e);
        } catch (java.util.concurrent.ExecutionException e) {
            if (e.getCause() instanceof org.apache.kafka.common.errors.TimeoutException) {
                throw new Exception("Kafka 操作超时，可能的原因：" +
                        "\n1. Kafka 服务器未启动" +
                        "\n2. 网络连接问题" +
                        "\n3. 防火墙阻止连接", e);
            } else {
                throw new Exception("Kafka 连接失败: " + e.getCause().getMessage(), e);
            }
        }
    }

    /**
     * Kafka LogEvent 反序列化器
     */
    public static class LogEventDeserializationSchema implements DeserializationSchema<LogEvent> {
        private final ObjectMapper objectMapper = new ObjectMapper();

        @Override
        public LogEvent deserialize(byte[] message) throws IOException {
            try {
                String jsonData = new String(message);
                System.out.println("✓ 接收到Kafka消息: " + jsonData);
                LogEvent logEvent = parseJsonToLogEvent(jsonData);
                if (logEvent != null) {
                    System.out.println("✓ 消息解析成功: " + logEvent.logType + ", user_id=" + logEvent.getFieldValue("user_id"));
                }
                return logEvent;
            } catch (Exception e) {
                System.err.println("Kafka消息反序列化失败: " + e.getMessage());
                return null;
            }
        }

        @Override
        public boolean isEndOfStream(LogEvent nextElement) {
            return false;
        }

        @Override
        public TypeInformation<LogEvent> getProducedType() {
            return TypeInformation.of(LogEvent.class);
        }

        private LogEvent parseJsonToLogEvent(String jsonData) {
            try {
                JsonNode rootNode = objectMapper.readTree(jsonData);

                // 提取固定字段
                String logType = rootNode.get("__log_type__").asText();
                String timestampStr = rootNode.get("__timestamp__").asText();
                ZonedDateTime timestamp;
                try {
                    // 优先尝试解析ISO_OFFSET_DATE_TIME格式
                    OffsetDateTime offsetDateTime = OffsetDateTime.parse(timestampStr, DateTimeFormatter.ISO_OFFSET_DATE_TIME);
                    timestamp = offsetDateTime.toZonedDateTime();
                } catch (Exception e1) {
                    // 如果解析失败，使用当前时间
                    timestamp = ZonedDateTime.now();
                    System.err.println("时间戳解析失败，使用当前时间: " + timestampStr + ", 错误: " + e1.getMessage());
                    throw e1;
                }

                // 提取其他动态字段
                Map<String, Object> fields = new HashMap<>();
                rootNode.fields().forEachRemaining(entry -> {
                    String fieldName = entry.getKey();
                    if (!"__log_type__".equals(fieldName) && !"__timestamp__".equals(fieldName)) {
                        fields.put(fieldName, entry.getValue().asText());
                    }
                });

                return new LogEvent(logType, timestamp, fields);
            } catch (Exception e) {
                System.err.println("解析JSON失败: " + e.getMessage());
                return null;
            }
        }
    }
}
