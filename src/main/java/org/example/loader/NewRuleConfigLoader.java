package org.example.loader;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.example.config.NewCEPRuleConfig;
import org.example.config.NewSubRuleGroup;
import org.example.config.WithinConfig;

import java.io.IOException;
import java.sql.*;
import java.util.List;

/**
 * 新的规则配置加载器
 */
public class NewRuleConfigLoader {
    private final String dbUrl;
    private final String dbUser;
    private final String dbPassword;
    private final ObjectMapper objectMapper;
    
    public NewRuleConfigLoader(String dbUrl, String dbUser, String dbPassword) {
        this.dbUrl = dbUrl;
        this.dbUser = dbUser;
        this.dbPassword = dbPassword;
        this.objectMapper = new ObjectMapper();
    }
    
    public NewCEPRuleConfig loadRuleConfig(String jobId) throws SQLException, IOException {
        String sql = "SELECT id, group_key, rules, within, status, add_time, update_time FROM zj_cep_rules WHERE id = ? AND status = 1";
        
        try (Connection conn = DriverManager.getConnection(dbUrl, dbUser, dbPassword);
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, jobId);
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next()) {
                NewCEPRuleConfig config = new NewCEPRuleConfig();
                config.id = rs.getString("id");
                config.groupKey = rs.getString("group_key");
                config.status = rs.getInt("status");
                config.addTime = rs.getTimestamp("add_time");
                config.updateTime = rs.getTimestamp("update_time");
                
                // 解析JSONB格式的rules字段
                String rulesJson = rs.getString("rules");
                config.rules = objectMapper.readValue(rulesJson, new TypeReference<List<NewSubRuleGroup>>() {});
                
                // 解析JSONB格式的within字段
                String withinJson = rs.getString("within");
                if (withinJson != null && !withinJson.trim().isEmpty()) {
                    config.within = objectMapper.readValue(withinJson, WithinConfig.class);
                }
                
                return config;
            }
        }
        
        return null;
    }
} 