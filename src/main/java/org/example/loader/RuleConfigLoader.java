package org.example.loader;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.example.config.CEPRuleConfig;
import org.example.config.SubRuleGroup;
import org.example.config.WithinConfig;

import java.io.IOException;
import java.sql.*;
import java.util.List;

/**
 * 规则配置加载器
 */
public class RuleConfigLoader {
    private final String dbUrl;
    private final String dbUser;
    private final String dbPassword;
    private final ObjectMapper objectMapper;
    
    public RuleConfigLoader(String dbUrl, String dbUser, String dbPassword) {
        this.dbUrl = dbUrl;
        this.dbUser = dbUser;
        this.dbPassword = dbPassword;
        this.objectMapper = new ObjectMapper();
    }
    
    public CEPRuleConfig loadRuleConfig(String jobId) throws SQLException, IOException {
        String sql = "SELECT id, rules, expression, within, status, add_time, update_time " +
                    "FROM zj_cep_rules WHERE id = ? AND status = 1";
        
        try (Connection conn = DriverManager.getConnection(dbUrl, dbUser, dbPassword);
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, jobId);
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next()) {
                CEPRuleConfig config = new CEPRuleConfig();
                config.id = rs.getString("id");
                config.expression = rs.getString("expression");
                config.status = rs.getInt("status");
                config.addTime = rs.getTimestamp("add_time");
                config.updateTime = rs.getTimestamp("update_time");
                
                // 解析JSON格式的rules字段
                String rulesJson = rs.getString("rules");
                config.rules = objectMapper.readValue(rulesJson, new TypeReference<List<SubRuleGroup>>() {});
                
                // 解析JSON格式的within字段
                String withinJson = rs.getString("within");
                config.within = objectMapper.readValue(withinJson, WithinConfig.class);
                
                return config;
            }
        }
        
        return null;
    }
} 