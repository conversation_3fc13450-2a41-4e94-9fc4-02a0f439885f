package org.example.sink;

import org.apache.flink.api.connector.sink2.Sink;
import org.apache.flink.api.connector.sink2.SinkWriter;
import org.apache.flink.api.connector.sink2.WriterInitContext;
import org.example.model.AlertEvent;
import org.example.model.LogEvent;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.ArrayNode;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * AlertEvent PostgreSQL Sink - 使用 Sink V2 API
 */
public class AlertEventPostgreSQLSink implements Sink<AlertEvent> {
    
    private final String dbUrl;
    private final String username;
    private final String password;
    
    public AlertEventPostgreSQLSink(String dbUrl, String username, String password) {
        this.dbUrl = dbUrl;
        this.username = username;
        this.password = password;
    }
    
    @Override
    public SinkWriter<AlertEvent> createWriter(WriterInitContext context) {
        return new AlertEventPostgreSQLSinkWriter(dbUrl, username, password);
    }
    
    /**
     * AlertEvent PostgreSQL Sink Writer 实现
     */
    public static class AlertEventPostgreSQLSinkWriter implements SinkWriter<AlertEvent> {
        private static final ObjectMapper objectMapper = new ObjectMapper();
        private Connection connection;
        private PreparedStatement insertStatement;
        
        public AlertEventPostgreSQLSinkWriter(String dbUrl, String username, String password) {
            try {
                // 加载驱动
                Class.forName("org.postgresql.Driver");
                
                // 建立连接
                connection = DriverManager.getConnection(dbUrl, username, password);
                
                // 自动创建表（如果不存在）
                //createTableIfNotExists();
                
                System.out.println("✅ 数据库表 zj_cep_alert_events prepareStatement");
                // 预编译SQL语句
                String sql = "INSERT INTO zj_cep_alert_events (group_key, alert_type, message, timestamp, rule_id, alert_content) VALUES (?, ?, ?, ?, ?, ?::jsonb)";
                insertStatement = connection.prepareStatement(sql);
                System.out.println("✅ 数据库表 zj_cep_alert_events prepareStatement success");
            } catch (Exception e) {
                throw new RuntimeException("Failed to initialize PostgreSQL connection", e);
            }
        }

        @Override
        public void write(AlertEvent alertEvent, Context context) {
            try {
                // 设置参数
                insertStatement.setString(1, alertEvent.groupKey);
                insertStatement.setString(2, alertEvent.alertType);
                insertStatement.setString(3, alertEvent.message);
                insertStatement.setTimestamp(4, Timestamp.valueOf(alertEvent.timestamp));
                insertStatement.setString(5, alertEvent.ruleId);
                
                // 将alertContent转换为JSON字符串
                try {
                    String alertContentJson = convertAlertContentToJson(alertEvent.alertContent);
                    insertStatement.setString(6, alertContentJson);
                } catch (Exception e) {
                    insertStatement.setString(6, "{}");
                }
                
                // 执行插入
                insertStatement.executeUpdate();
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
        }
        
        @Override
        public void flush(boolean endOfInput) {
            // 不需要特殊处理，因为每次write都会立即执行
        }
        
        @Override
        public void close() throws Exception {
            if (insertStatement != null) {
                insertStatement.close();
            }
            if (connection != null) {
                connection.close();
            }
        }
        
        /**
         * 将alertContent转换为简化的JSON格式，避免序列化复杂对象
         */
        private static String convertAlertContentToJson(Map<String, List<LogEvent>> alertContent) throws Exception {
            if (alertContent == null) {
                return "{}";
            }
            
            ObjectNode root = objectMapper.createObjectNode();
            
            for (Map.Entry<String, List<LogEvent>> entry : alertContent.entrySet()) {
                String stepName = entry.getKey();
                List<LogEvent> events = entry.getValue();
                
                ArrayNode eventsArray = objectMapper.createArrayNode();
                for (LogEvent event : events) {
                    ObjectNode eventNode = objectMapper.createObjectNode();
                    eventNode.put("logType", event.logType);
                    eventNode.put("timestamp", event.timestamp.toString());
                    
                    // 添加fields信息（简化版）
                    ObjectNode fieldsNode = objectMapper.createObjectNode();
                    for (Map.Entry<String, Object> field : event.fields.entrySet()) {
                        fieldsNode.put(field.getKey(), field.getValue().toString());
                    }
                    eventNode.set("fields", fieldsNode);
                    
                    // 添加匹配的规则信息
                    if (event.matchedRules != null && !event.matchedRules.isEmpty()) {
                        ArrayNode rulesArray = objectMapper.createArrayNode();
                        for (String rule : event.matchedRules) {
                            rulesArray.add(rule);
                        }
                        eventNode.set("matchedRules", rulesArray);
                    }
                    
                    eventsArray.add(eventNode);
                }
                
                root.set(stepName, eventsArray);
            }
            
            return objectMapper.writeValueAsString(root);
        }
    }
} 