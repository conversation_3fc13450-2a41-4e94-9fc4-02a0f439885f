/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.example;

import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.cep.CEP;
import org.apache.flink.cep.PatternSelectFunction;
import org.apache.flink.cep.PatternStream;
import org.apache.flink.cep.pattern.Pattern;
import org.apache.flink.cep.pattern.conditions.SimpleCondition;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.windowing.assigners.SlidingEventTimeWindows;
import org.apache.flink.streaming.api.functions.windowing.WindowFunction;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;
import org.apache.flink.api.connector.sink2.Sink;
import org.apache.flink.api.connector.sink2.SinkWriter;
import org.apache.flink.api.connector.sink2.WriterInitContext;
import org.apache.flink.configuration.Configuration;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Flink CEP作业，用于分析认证和连接日志
 * 检测两种情况：
 * 1. 连接失败且10秒内有认证成功 -> 网络不通
 * 2. 反复连接失败/成功但无认证日志 -> 网络质量差
 */
public class LogAnalysisCEPJob {

    public static void main(String[] args) throws Exception {
        // 设置执行环境
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1); // 为了简化，设置并行度为1

        // 数据源1：从Socket获取认证日志流 (端口9999)
        DataStream<String> authSocketStream = env.socketTextStream("localhost", 9999);
        DataStream<LogEvent> authLogStream = authSocketStream
                .map(new AuthLogParser())
                .filter(Objects::nonNull) // 过滤解析失败的数据
                .assignTimestampsAndWatermarks(
                        WatermarkStrategy.<LogEvent>forBoundedOutOfOrderness(Duration.ofSeconds(5))
                                .withTimestampAssigner((event, timestamp) ->
                                        event.timestamp.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli())
                );

        // 数据源2：从Socket获取连接日志流 (端口9998)
        DataStream<String> connectionSocketStream = env.socketTextStream("localhost", 9998);
        DataStream<LogEvent> connectionLogStream = connectionSocketStream
                .map(new ConnectionLogParser())
                .filter(Objects::nonNull) // 过滤解析失败的数据
                .assignTimestampsAndWatermarks(
                        WatermarkStrategy.<LogEvent>forBoundedOutOfOrderness(Duration.ofSeconds(5))
                                .withTimestampAssigner((event, timestamp) ->
                                        event.timestamp.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli())
                );

        // 合并两个数据流
        DataStream<LogEvent> logStream = authLogStream.union(connectionLogStream);

        // 添加滑动窗口：窗口大小10秒，滑动步长1秒
        DataStream<LogEvent> windowedLogStream = logStream
                .keyBy(event -> event.userId)
                .window(SlidingEventTimeWindows.of(Duration.ofSeconds(10), Duration.ofSeconds(1)))
                .apply(new WindowFunction<LogEvent, LogEvent, String, TimeWindow>() {
                    @Override
                    public void apply(String key, TimeWindow window, Iterable<LogEvent> events, Collector<LogEvent> collector) throws Exception {
                        // 将窗口内的事件重新发出
                        for (LogEvent event : events) {
                            collector.collect(event);
                        }
                    }
                });

        // 模式1：检测网络不通情况
        // 认证成功后10秒内有连接失败
        Pattern<LogEvent, ?> networkUnavailablePattern = Pattern.<LogEvent>begin("authSuccess")
                .where(new SimpleCondition<LogEvent>() {
                    @Override
                    public boolean filter(LogEvent event) {
                        return event.logType == LogType.AUTH_SUCCESS;
                    }
                })
                .followedBy("connectionFailed")
                .where(new SimpleCondition<LogEvent>() {
                    @Override
                    public boolean filter(LogEvent event) {
                        return event.logType == LogType.CONNECTION_FAILED;
                    }
                })
                .within(Duration.ofSeconds(10));

        PatternStream<LogEvent> networkUnavailableStream = CEP.pattern(
                windowedLogStream.keyBy(event -> event.userId),
                networkUnavailablePattern
        );

        // 处理网络不通模式
        DataStream<AlertEvent> networkUnavailableAlerts = networkUnavailableStream.select(
                new PatternSelectFunction<LogEvent, AlertEvent>() {
                    @Override
                    public AlertEvent select(Map<String, List<LogEvent>> pattern) {
                        LogEvent authEvent = pattern.get("authSuccess").get(0);
                        return new AlertEvent(
                                authEvent.userId,
                                AlertType.NETWORK_UNAVAILABLE,
                                String.format("用户 %s 认证成功后连接失败，网络不通", authEvent.userId),
                                LocalDateTime.now()
                        );
                    }
                }
        );

        // 模式2：检测网络质量差情况
        // 连续出现连接失败和连接成功，且期间没有认证日志
        Pattern<LogEvent, ?> poorNetworkQualityPattern = Pattern.<LogEvent>begin("firstConnectionFailed")
                .where(new SimpleCondition<LogEvent>() {
                    @Override
                    public boolean filter(LogEvent event) {
                        return event.logType != LogType.CONNECTION_SUCCESS;
                    }
                })
                .followedBy("connectionSuccess")
                .where(new SimpleCondition<LogEvent>() {
                    @Override
                    public boolean filter(LogEvent event) {
                        return event.logType == LogType.CONNECTION_SUCCESS;
                    }
                })
                .followedBy("secondConnectionFailed")
                .where(new SimpleCondition<LogEvent>() {
                    @Override
                    public boolean filter(LogEvent event) {
                        return event.logType != LogType.CONNECTION_SUCCESS;
                    }
                })
                .within(Duration.ofSeconds(30));

        // 为连接日志也添加滑动窗口
        DataStream<LogEvent> windowedConnectionLogStream = connectionLogStream
                .keyBy(event -> event.userId)
                .window(SlidingEventTimeWindows.of(Duration.ofSeconds(10), Duration.ofSeconds(1)))
                .apply(new WindowFunction<LogEvent, LogEvent, String, TimeWindow>() {
                    @Override
                    public void apply(String key, TimeWindow window, Iterable<LogEvent> events, Collector<LogEvent> collector) throws Exception {
                        // 将窗口内的事件重新发出
                        for (LogEvent event : events) {
                            collector.collect(event);
                        }
                    }
                });

        PatternStream<LogEvent> poorNetworkQualityStream = CEP.pattern(
                windowedConnectionLogStream.keyBy(event -> event.userId),
                poorNetworkQualityPattern
        );

        // 处理网络质量差模式
        DataStream<AlertEvent> poorNetworkQualityAlerts = poorNetworkQualityStream.select(
                new PatternSelectFunction<LogEvent, AlertEvent>() {
                    @Override
                    public AlertEvent select(Map<String, List<LogEvent>> pattern) {
                        LogEvent firstEvent = pattern.get("firstConnectionFailed").get(0);
                        return new AlertEvent(
                                firstEvent.userId,
                                AlertType.POOR_NETWORK_QUALITY,
                                String.format("用户 %s 反复出现连接失败和成功，网络质量差", firstEvent.userId),
                                LocalDateTime.now()
                        );
                    }
                }
        );

        // 创建自定义的PostgreSQL Sink
        PostgreSQLSink postgresSink = new PostgreSQLSink();

        // 将告警结果写入数据库
        networkUnavailableAlerts.sinkTo(postgresSink);
        poorNetworkQualityAlerts.sinkTo(postgresSink);

        // 同时保留控制台输出用于调试
        networkUnavailableAlerts.print("网络不通告警");
        poorNetworkQualityAlerts.print("网络质量差告警");

        // 执行作业
        env.execute("Log Analysis CEP Job");
    }

    /**
     * 日志事件类
     */
    public static class LogEvent {
        public String userId;
        public LogType logType;
        public LocalDateTime timestamp;

        public LogEvent() {
        }

        public LogEvent(String userId, LogType logType, LocalDateTime timestamp) {
            this.userId = userId;
            this.logType = logType;
            this.timestamp = timestamp;
        }

        @Override
        public String toString() {
            return String.format("LogEvent{userId='%s', logType=%s, timestamp=%s}",
                    userId, logType, timestamp);
        }
    }

    /**
     * 日志类型枚举
     */
    public enum LogType {
        AUTH_SUCCESS,       // 认证成功
        CONNECTION_SUCCESS, // 连接成功
        CONNECTION_FAILED   // 连接失败
    }

    /**
     * 告警事件类
     */
    public static class AlertEvent {
        public String userId;
        public AlertType alertType;
        public String message;
        public LocalDateTime timestamp;

        public AlertEvent() {
        }

        public AlertEvent(String userId, AlertType alertType, String message, LocalDateTime timestamp) {
            this.userId = userId;
            this.alertType = alertType;
            this.message = message;
            this.timestamp = timestamp;
        }

        @Override
        public String toString() {
            return String.format("AlertEvent{userId='%s', alertType=%s, message='%s', timestamp=%s}",
                    userId, alertType, message, timestamp);
        }
    }

    /**
     * 告警类型枚举
     */
    public enum AlertType {
        NETWORK_UNAVAILABLE,    // 网络不通
        POOR_NETWORK_QUALITY    // 网络质量差
    }

    /**
     * 认证日志解析器
     * 期望格式: "userId,AUTH_SUCCESS,timestamp"
     * 示例: "user1,AUTH_SUCCESS,2024-01-01T12:00:00"
     */
    public static class AuthLogParser implements MapFunction<String, LogEvent> {
        @Override
        public LogEvent map(String line) throws Exception {
            try {
                String[] parts = line.split(",");
                if (parts.length >= 2) {
                    String userId = parts[0].trim();
                    String logTypeStr = parts[1].trim();

                    // 只处理认证相关的日志
                    if ("AUTH_SUCCESS".equals(logTypeStr)) {
                        LocalDateTime timestamp = parts.length >= 3 && !parts[2].trim().isEmpty()
                                ? LocalDateTime.parse(parts[2].trim())
                                : LocalDateTime.now();
                        return new LogEvent(userId, LogType.AUTH_SUCCESS, timestamp);
                    }
                }
            } catch (Exception e) {
                System.err.println("认证日志解析失败: " + line + ", 错误: " + e.getMessage());
            }
            return null; // 解析失败返回null，会被filter过滤掉
        }
    }

    /**
     * 连接日志解析器
     * 期望格式: "userId,CONNECTION_SUCCESS/CONNECTION_FAILED,timestamp"
     * 示例: "user1,CONNECTION_FAILED,2024-01-01T12:00:05"
     */
    public static class ConnectionLogParser implements MapFunction<String, LogEvent> {
        @Override
        public LogEvent map(String line) throws Exception {
            try {
                String[] parts = line.split(",");
                if (parts.length >= 2) {
                    String userId = parts[0].trim();
                    String logTypeStr = parts[1].trim();

                    LogType logType = null;
                    if ("CONNECTION_SUCCESS".equals(logTypeStr)) {
                        logType = LogType.CONNECTION_SUCCESS;
                    } else if ("CONNECTION_FAILED".equals(logTypeStr)) {
                        logType = LogType.CONNECTION_FAILED;
                    }

                    if (logType != null) {
                        LocalDateTime timestamp = parts.length >= 3 && !parts[2].trim().isEmpty()
                                ? LocalDateTime.parse(parts[2].trim())
                                : LocalDateTime.now();
                        return new LogEvent(userId, logType, timestamp);
                    }
                }
            } catch (Exception e) {
                System.err.println("连接日志解析失败: " + line + ", 错误: " + e.getMessage());
            }
            return null; // 解析失败返回null，会被filter过滤掉
        }
    }

    /**
     * 自定义PostgreSQL Sink - 使用新的 Sink V2 API
     */
    public static class PostgreSQLSink implements Sink<AlertEvent> {
        @Override
        public SinkWriter<AlertEvent> createWriter(WriterInitContext context) {
            return new PostgreSQLSinkWriter();
        }
    }

    /**
     * PostgreSQL Sink Writer 实现
     */
    public static class PostgreSQLSinkWriter implements SinkWriter<AlertEvent> {
        private java.sql.Connection connection;
        private java.sql.PreparedStatement insertStatement;

                public PostgreSQLSinkWriter() {
            try {
                // 数据库连接配置
                String url = "***************************************************************************************************************";
                String username = "admin";
                String password = "JAY9^yyds#68";
                String driverClass = "org.postgresql.Driver";

                // 加载驱动
                Class.forName(driverClass);
                
                // 建立连接
                connection = java.sql.DriverManager.getConnection(url, username, password);
                //connection.setAutoCommit(false);

                // 自动创建表（如果不存在）
                //createTableIfNotExists();

                System.out.println("✅ 数据库表 zj_alert_events prepareStatement");
                // 预编译SQL语句
                String sql = "INSERT INTO zj_alert_events (user_id, alert_type, message, timestamp) VALUES (?, ?, ?, ?)";
                insertStatement = connection.prepareStatement(sql);
                System.out.println("✅ 数据库表 zj_alert_events prepareStatement success");
            } catch (Exception e) {
                throw new RuntimeException("Failed to initialize PostgreSQL connection", e);
            }
        }


        @Override
        public void write(AlertEvent alertEvent, Context context) {
            try {
                // 设置参数
                insertStatement.setString(1, alertEvent.userId);
                insertStatement.setString(2, alertEvent.alertType.name());
                insertStatement.setString(3, alertEvent.message);
                insertStatement.setTimestamp(4, java.sql.Timestamp.valueOf(alertEvent.timestamp));

                // 执行插入
                insertStatement.executeUpdate();

                // 提交事务
                //connection.commit();
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
        }

        @Override
        public void flush(boolean endOfInput) {
            try {
                // 确保所有数据都已提交
                if (connection != null && !connection.isClosed()) {
                    //connection.commit();
                }
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
        }

        @Override
        public void close() throws Exception {
            if (insertStatement != null) {
                insertStatement.close();
            }
            if (connection != null) {
                connection.close();
            }
        }
    }
} 