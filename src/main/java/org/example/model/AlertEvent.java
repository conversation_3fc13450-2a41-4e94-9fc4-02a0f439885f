package org.example.model;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 告警事件类
 */
public class AlertEvent {
    public String groupKey;        // 分组键值
    public String alertType;       // 告警类型
    public String message;         // 告警消息
    public LocalDateTime timestamp; // 告警时间
    public String ruleId;          // 规则ID
    public Map<String, List<LogEvent>> alertContent; // 告警内容(匹配的Pattern)
    
    public AlertEvent() {}
    
    public AlertEvent(String groupKey, String alertType, String message, LocalDateTime timestamp) {
        this.groupKey = groupKey;
        this.alertType = alertType;
        this.message = message;
        this.timestamp = timestamp;
    }
    
    public AlertEvent(String groupKey, String alertType, String message, LocalDateTime timestamp, 
                     String ruleId, Map<String, List<LogEvent>> alertContent) {
        this.groupKey = groupKey;
        this.alertType = alertType;
        this.message = message;
        this.timestamp = timestamp;
        this.ruleId = ruleId;
        this.alertContent = alertContent;
    }
    
    @Override
    public String toString() {
        return String.format("AlertEvent{groupKey='%s', alertType='%s', message='%s', timestamp=%s, ruleId='%s', alertContent=%s}",
            groupKey, alertType, message, timestamp, ruleId, alertContent != null ? alertContent.keySet() : "null");
    }
}