package org.example.model;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 日志事件类
 */
public class LogEvent {
    public String logType;  // 对应 __log_type__ 字段
    public ZonedDateTime timestamp;  // 对应 __timestamp__ 字段
    public Map<String, Object> fields; // 其他动态字段
    public Set<String> matchedRules; // 记录命中的子规则ID
    
    public LogEvent() {
        this.fields = new HashMap<>();
        this.matchedRules = new HashSet<>();
    }
    
    public LogEvent(String logType, ZonedDateTime timestamp, Map<String, Object> fields) {
        this.logType = logType;
        this.timestamp = timestamp;
        this.fields = fields != null ? fields : new HashMap<>();
        this.matchedRules = new HashSet<>();
    }
    
    /**
     * 获取字段值
     */
    public String getFieldValue(String fieldName) {
        Object value = fields.get(fieldName);
        return value != null ? value.toString() : null;
    }
    
    /**
     * 添加命中的子规则
     */
    public void addMatchedRule(String ruleId) {
        this.matchedRules.add(ruleId);
    }
    
    /**
     * 检查是否包含指定的子规则
     */
    public boolean hasMatchedRule(String ruleId) {
        return this.matchedRules.contains(ruleId);
    }
    
    /**
     * 添加额外字段
     */
    public void addExtraField(String key, String value) {
        this.fields.put(key, value);
    }
    
    /**
     * 复制LogEvent并添加命中的规则
     */
    public LogEvent copyWithMatchedRule(String ruleId) {
        LogEvent copy = new LogEvent(this.logType, this.timestamp, new HashMap<>(this.fields));
        copy.matchedRules.addAll(this.matchedRules);
        copy.matchedRules.add(ruleId);
        return copy;
    }
    
    @Override
    public String toString() {
        return String.format("LogEvent{logType='%s', timestamp=%s, fields=%s, matchedRules=%s}", 
            logType, timestamp, fields, matchedRules);
    }
} 