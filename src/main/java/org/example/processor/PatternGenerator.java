package org.example.processor;

import org.apache.flink.cep.pattern.Pattern;
import org.apache.flink.cep.pattern.conditions.SimpleCondition;
import org.example.config.CEPRuleConfig;
import org.example.config.SubRule;
import org.example.config.SubRuleGroup;
import org.example.config.WithinConfig;
import org.example.model.LogEvent;

/**
 * Pattern生成器
 */
public class PatternGenerator {
    
    public Pattern<LogEvent, ?> generatePattern(SubRuleGroup subRuleGroup, WithinConfig within) {
        if (subRuleGroup.rules.isEmpty()) {
            throw new IllegalArgumentException("子规则不能为空");
        }
        
        Pattern<LogEvent, ?> pattern = null;
        
        for (int i = 0; i < subRuleGroup.rules.size(); i++) {
            SubRule rule = subRuleGroup.rules.get(i);
            String stepName = "step_" + i;
            
            SimpleCondition<LogEvent> condition = new SimpleCondition<LogEvent>() {
                @Override
                public boolean filter(LogEvent event) {
                    String fieldValue = event.getFieldValue(rule.matchKey);
                    if (fieldValue == null) {
                        return false;
                    }
                    
                    switch (rule.matchType) {
                        case "=":
                            return fieldValue.equals(rule.matchVal);
                        case "!=":
                            return !fieldValue.equals(rule.matchVal);
                        case "contains":
                            return fieldValue.contains(rule.matchVal);
                        default:
                            return fieldValue.equals(rule.matchVal);
                    }
                }
            };
            
            if (pattern == null) {
                // 第一个条件
                pattern = Pattern.<LogEvent>begin(stepName).where(condition);
                
                // 处理重复次数
                int times = Integer.parseInt(rule.matchTimes);
                if (times > 1) {
                    pattern = pattern.timesOrMore(times);
                }
            } else {
                // 后续条件
                pattern = pattern.followedBy(stepName).where(condition);
                
                // 处理重复次数
                int times = Integer.parseInt(rule.matchTimes);
                if (times > 1) {
                    pattern = pattern.timesOrMore(times);
                }
            }
        }
        
        // 设置时间窗口
        if (within != null) {
            pattern = pattern.within(within.toDuration());
        }
        
        return pattern;
    }
    
    /**
     * 生成组合Pattern - 所有子规则组都必须匹配
     */
    public Pattern<LogEvent, ?> generateCombinedPattern(CEPRuleConfig ruleConfig) {
        if (ruleConfig.rules.isEmpty()) {
            throw new IllegalArgumentException("规则配置不能为空");
        }
        
        Pattern<LogEvent, ?> combinedPattern = null;
        
        for (int i = 0; i < ruleConfig.rules.size(); i++) {
            SubRuleGroup subRuleGroup = ruleConfig.rules.get(i);
            String stepName = "rule_group_" + i;
            
            // 为每个子规则组创建复合条件
            SimpleCondition<LogEvent> groupCondition = createGroupCondition(subRuleGroup);
            
            if (combinedPattern == null) {
                // 第一个规则组
                combinedPattern = Pattern.<LogEvent>begin(stepName).where(groupCondition);
            } else {
                // 后续规则组 - 使用followedBy确保所有规则都匹配
                combinedPattern = combinedPattern.followedBy(stepName).where(groupCondition);
            }
        }
        
        // 设置时间窗口
        if (ruleConfig.within != null) {
            combinedPattern = combinedPattern.within(ruleConfig.within.toDuration());
        }
        
        return combinedPattern;
    }
    
    /**
     * 为子规则组创建复合条件
     */
    private SimpleCondition<LogEvent> createGroupCondition(SubRuleGroup subRuleGroup) {
        return new SimpleCondition<LogEvent>() {
            @Override
            public boolean filter(LogEvent event) {
                // 首先检查日志类型是否匹配
                if (!subRuleGroup.source.equals(event.logType)) {
                    return false;
                }
                
                // 检查所有子规则是否都匹配
                for (SubRule rule : subRuleGroup.rules) {
                    String fieldValue = event.getFieldValue(rule.matchKey);
                    if (fieldValue == null) {
                        return false;
                    }
                    
                    boolean ruleMatches = false;
                    switch (rule.matchType) {
                        case "=":
                            ruleMatches = fieldValue.equals(rule.matchVal);
                            break;
                        case "!=":
                            ruleMatches = !fieldValue.equals(rule.matchVal);
                            break;
                        case "contains":
                            ruleMatches = fieldValue.contains(rule.matchVal);
                            break;
                        default:
                            ruleMatches = fieldValue.equals(rule.matchVal);
                    }
                    
                    // 如果任何一个子规则不匹配，整个规则组就不匹配
                    if (!ruleMatches) {
                        return false;
                    }
                }
                
                // 所有子规则都匹配
                return true;
            }
        };
    }
} 