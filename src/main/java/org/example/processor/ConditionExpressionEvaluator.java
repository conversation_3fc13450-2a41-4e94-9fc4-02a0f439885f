package org.example.processor;

import org.example.config.Condition;
import org.example.model.LogEvent;

import java.util.List;
import java.util.Map;
import java.util.Stack;
import java.util.stream.Collectors;

/**
 * 增强的条件表达式求值器
 * 支持复杂嵌套括号和 AND, OR, NOT 逻辑操作
 * 操作符优先级：NOT > AND > OR
 */
public class ConditionExpressionEvaluator {
    
    /**
     * 评估条件表达式
     * @param expression 条件表达式，如 "(1 AND (2 OR (5 AND 6)))"
     * @param conditions 条件列表
     * @param event 日志事件
     * @return 表达式结果
     */
    public static boolean evaluate(String expression, List<Condition> conditions, LogEvent event) {
        if (expression == null || expression.trim().isEmpty()) {
            return true;
        }
        
        // 创建条件ID到条件对象的映射
        Map<String, Condition> conditionMap = conditions.stream()
            .collect(Collectors.toMap(c -> c.id, c -> c));
        
        try {
            return evaluateExpression(expression.trim(), conditionMap, event);
        } catch (Exception e) {
            System.err.println("条件表达式解析错误: " + expression + ", 错误: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 使用递归下降解析器处理复杂嵌套表达式
     */
    private static boolean evaluateExpression(String expression, Map<String, Condition> conditionMap, LogEvent event) {
        // 移除多余空格
        expression = expression.replaceAll("\\s+", " ").trim();
        
        // 使用递归下降解析
        ExpressionParser parser = new ExpressionParser(expression, conditionMap, event);
        return parser.parseOrExpression();
    }
    
    /**
     * 表达式解析器类
     */
    private static class ExpressionParser {
        private final String expression;
        private final Map<String, Condition> conditionMap;
        private final LogEvent event;
        private int position = 0;
        
        public ExpressionParser(String expression, Map<String, Condition> conditionMap, LogEvent event) {
            this.expression = expression;
            this.conditionMap = conditionMap;
            this.event = event;
        }
        
        /**
         * 解析OR表达式 (最低优先级)
         */
        public boolean parseOrExpression() {
            boolean result = parseAndExpression();
            
            while (position < expression.length()) {
                skipWhitespace();
                if (matchKeyword("OR")) {
                    boolean right = parseAndExpression();
                    result = result || right;
                } else {
                    break;
                }
            }
            
            return result;
        }
        
        /**
         * 解析AND表达式 (中等优先级)
         */
        private boolean parseAndExpression() {
            boolean result = parseNotExpression();
            
            while (position < expression.length()) {
                skipWhitespace();
                if (matchKeyword("AND")) {
                    boolean right = parseNotExpression();
                    result = result && right;
                } else {
                    break;
                }
            }
            
            return result;
        }
        
        /**
         * 解析NOT表达式 (最高优先级)
         */
        private boolean parseNotExpression() {
            skipWhitespace();
            
            if (matchKeyword("NOT")) {
                return !parsePrimaryExpression();
            } else {
                return parsePrimaryExpression();
            }
        }
        
        /**
         * 解析基础表达式 (条件ID 或 括号表达式)
         */
        private boolean parsePrimaryExpression() {
            skipWhitespace();
            
            // 处理括号
            if (position < expression.length() && expression.charAt(position) == '(') {
                position++; // 跳过 '('
                boolean result = parseOrExpression();
                skipWhitespace();
                if (position < expression.length() && expression.charAt(position) == ')') {
                    position++; // 跳过 ')'
                } else {
                    throw new IllegalArgumentException("缺少右括号 ')' 在位置: " + position);
                }
                return result;
            }
            
            // 处理条件ID或布尔值
            String token = parseToken();
            
            // 处理布尔值
            if ("true".equalsIgnoreCase(token)) {
                return true;
            }
            if ("false".equalsIgnoreCase(token)) {
                return false;
            }
            
            // 处理条件ID
            Condition condition = conditionMap.get(token);
            if (condition != null) {
                return evaluateCondition(condition, event);
            }
            
            throw new IllegalArgumentException("未知的条件ID: " + token);
        }
        
        /**
         * 解析下一个令牌
         */
        private String parseToken() {
            skipWhitespace();
            
            if (position >= expression.length()) {
                throw new IllegalArgumentException("表达式意外结束");
            }
            
            int start = position;
            while (position < expression.length()) {
                char c = expression.charAt(position);
                if (Character.isWhitespace(c) || c == '(' || c == ')') {
                    break;
                }
                position++;
            }
            
            if (start == position) {
                throw new IllegalArgumentException("无效的令牌在位置: " + position);
            }
            
            return expression.substring(start, position);
        }
        
        /**
         * 匹配关键字
         */
        private boolean matchKeyword(String keyword) {
            skipWhitespace();
            
            if (position + keyword.length() <= expression.length()) {
                String substr = expression.substring(position, position + keyword.length());
                if (substr.equalsIgnoreCase(keyword)) {
                    // 确保关键字后面是空格、括号或表达式结束
                    int nextPos = position + keyword.length();
                    if (nextPos >= expression.length() || 
                        Character.isWhitespace(expression.charAt(nextPos)) ||
                        expression.charAt(nextPos) == '(' || 
                        expression.charAt(nextPos) == ')') {
                        position = nextPos;
                        return true;
                    }
                }
            }
            return false;
        }
        
        /**
         * 跳过空白字符
         */
        private void skipWhitespace() {
            while (position < expression.length() && Character.isWhitespace(expression.charAt(position))) {
                position++;
            }
        }
    }
    
    /**
     * 评估单个条件
     */
    private static boolean evaluateCondition(Condition condition, LogEvent event) {
        String fieldValue = event.getFieldValue(condition.matchKey);
        // System.out.printf("    评估条件 %s: 字段=%s, 值=%s, 匹配类型=%s, 期望值=%s\n", condition.id, condition.matchKey, fieldValue, condition.matchType, condition.matchVal);
        
        if (fieldValue == null) {
            // System.out.printf("    条件 %s: 字段值为null，返回false\n", condition.id);
            return false;
        }
        
        boolean result;
        switch (condition.matchType.toLowerCase()) {
            case "=":
            case "eq":
            case "equals":
                result = fieldValue.equals(condition.matchVal);
                // System.out.printf("    条件 %s: %s = %s => %s\n", condition.id, fieldValue, condition.matchVal, result);
                return result;
            case "!=":
            case "ne":
            case "not_equals":
                result = !fieldValue.equals(condition.matchVal);
                // System.out.printf("    条件 %s: %s != %s => %s\n", condition.id, fieldValue, condition.matchVal, result);
                return result;
            case "contains":
                return fieldValue.contains(condition.matchVal);
            case "not_contains":
                return !fieldValue.contains(condition.matchVal);
            case "starts_with":
            case "startswith":
                return fieldValue.startsWith(condition.matchVal);
            case "ends_with":
            case "endswith":
                return fieldValue.endsWith(condition.matchVal);
            case ">":
            case "gt":
                try {
                    return Double.parseDouble(fieldValue) > Double.parseDouble(condition.matchVal);
                } catch (NumberFormatException e) {
                    return false;
                }
            case "<":
            case "lt":
                try {
                    return Double.parseDouble(fieldValue) < Double.parseDouble(condition.matchVal);
                } catch (NumberFormatException e) {
                    return false;
                }
            case ">=":
            case "ge":
            case "gte":
                try {
                    return Double.parseDouble(fieldValue) >= Double.parseDouble(condition.matchVal);
                } catch (NumberFormatException e) {
                    return false;
                }
            case "<=":
            case "le":
            case "lte":
                try {
                    return Double.parseDouble(fieldValue) <= Double.parseDouble(condition.matchVal);
                } catch (NumberFormatException e) {
                    return false;
                }
            case "regex":
            case "matches":
                try {
                    return fieldValue.matches(condition.matchVal);
                } catch (Exception e) {
                    // System.err.println("正则表达式匹配错误: " + condition.matchVal + ", 错误: " + e.getMessage());
                    return false;
                }
            default:
                result = fieldValue.equals(condition.matchVal);
                // System.out.printf("    条件 %s: %s = %s (default) => %s\n", condition.id, fieldValue, condition.matchVal, result);
                return result;
        }
    }
} 