package org.example.source;

import org.apache.flink.api.connector.source.SourceSplit;

/**
 * NATS日志事件分片
 */
public class NatsLogEventSplit implements SourceSplit {
    private final String subject;
    
    public NatsLogEventSplit(String subject) {
        this.subject = subject;
    }
    
    @Override
    public String splitId() {
        return "nats_split_" + subject;
    }
    
    public String getSubject() {
        return subject;
    }
} 