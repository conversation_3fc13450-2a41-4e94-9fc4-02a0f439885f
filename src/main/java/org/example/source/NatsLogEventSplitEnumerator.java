package org.example.source;

import org.apache.flink.api.connector.source.SplitEnumerator;
import org.apache.flink.api.connector.source.SplitEnumeratorContext;

import java.util.List;

/**
 * NATS分片枚举器
 */
public class NatsLogEventSplitEnumerator implements SplitEnumerator<NatsLogEventSplit, Void> {
    private final String subject;
    private final SplitEnumeratorContext<NatsLogEventSplit> context;
    private boolean splitAssigned = false;
    
    public NatsLogEventSplitEnumerator(String subject, SplitEnumeratorContext<NatsLogEventSplit> context) {
        this.subject = subject;
        this.context = context;
    }
    
    @Override
    public void start() {
        // 启动枚举器
    }
    
    @Override
    public void handleSplitRequest(int subtaskId, String requesterHostname) {
        if (!splitAssigned) {
            context.assignSplit(new NatsLogEventSplit(subject), subtaskId);
            splitAssigned = true;
        }
    }
    
    @Override
    public void addSplitsBack(List<NatsLogEventSplit> splits, int subtaskId) {
        // 重新分配分片
        splitAssigned = false;
    }
    
    @Override
    public void addReader(int subtaskId) {
        if (!splitAssigned) {
            context.assignSplit(new NatsLogEventSplit(subject), subtaskId);
            splitAssigned = true;
        }
    }
    
    @Override
    public Void snapshotState(long checkpointId) {
        return null;
    }
    
    @Override
    public void close() {
        // 清理资源
    }
} 