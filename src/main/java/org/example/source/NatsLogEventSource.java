package org.example.source;

import org.apache.flink.api.connector.source.*;
import org.apache.flink.core.io.SimpleVersionedSerializer;
import org.example.model.LogEvent;

/**
 * NATS日志事件源
 */
public class NatsLogEventSource implements Source<LogEvent, NatsLogEventSplit, Void> {
    private final String natsUrl;
    private final String subject;
    private final String username;
    private final String password;
    private final String group;
    
    public NatsLogEventSource(String natsUrl, String subject) {
        this(natsUrl, subject, null, null, null);
    }
    
    public NatsLogEventSource(String natsUrl, String subject, String group) {
        this(natsUrl, subject, null, null, group);
    }
    
    public NatsLogEventSource(String natsUrl, String subject, String username, String password) {
        this(natsUrl, subject, username, password, null);
    }
    
    public NatsLogEventSource(String natsUrl, String subject, String username, String password, String group) {
        this.natsUrl = natsUrl;
        this.subject = subject;
        this.username = username;
        this.password = password;
        this.group = group;
    }
    
    @Override
    public Boundedness getBoundedness() {
        return Boundedness.CONTINUOUS_UNBOUNDED;
    }
    
    @Override
    public SourceReader<LogEvent, NatsLogEventSplit> createReader(SourceReaderContext readerContext) {
        return new NatsLogEventSourceReader(natsUrl, subject, username, password, group);
    }
    
    @Override
    public SplitEnumerator<NatsLogEventSplit, Void> createEnumerator(SplitEnumeratorContext<NatsLogEventSplit> enumContext) {
        return new NatsLogEventSplitEnumerator(subject, enumContext);
    }
    
    @Override
    public SplitEnumerator<NatsLogEventSplit, Void> restoreEnumerator(SplitEnumeratorContext<NatsLogEventSplit> enumContext, Void checkpoint) {
        return createEnumerator(enumContext);
    }
    
    @Override
    public SimpleVersionedSerializer<NatsLogEventSplit> getSplitSerializer() {
        return new NatsLogEventSplitSerializer();
    }
    
    @Override
    public SimpleVersionedSerializer<Void> getEnumeratorCheckpointSerializer() {
        return new SimpleVersionedSerializer<Void>() {
            @Override
            public int getVersion() {
                return 1;
            }
            
            @Override
            public byte[] serialize(Void obj) {
                return new byte[0];
            }
            
            @Override
            public Void deserialize(int version, byte[] serialized) {
                return null;
            }
        };
    }
} 