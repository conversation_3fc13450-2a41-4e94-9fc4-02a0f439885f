package org.example.source;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.nats.client.*;
import org.apache.flink.api.connector.source.ReaderOutput;
import org.apache.flink.api.connector.source.SourceReader;
import org.apache.flink.core.io.InputStatus;

import java.time.ZonedDateTime;
import java.util.concurrent.CompletableFuture;
import org.example.model.LogEvent;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.OffsetDateTime;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * NATS日志事件源读取器
 */
public class NatsLogEventSourceReader implements SourceReader<LogEvent, NatsLogEventSplit> {
    private final String natsUrl;
    private final String subject;
    private final String username;
    private final String password;
    private final String group;
    private final ObjectMapper objectMapper;
    private final BlockingQueue<LogEvent> eventQueue;
    private Connection natsConnection;
    private Subscription subscription;
    private Dispatcher dispatcher;
    
    public NatsLogEventSourceReader(String natsUrl, String subject) {
        this(natsUrl, subject, null, null, null);
    }
    
    public NatsLogEventSourceReader(String natsUrl, String subject, String username, String password) {
        this(natsUrl, subject, username, password, null);
    }
    
    public NatsLogEventSourceReader(String natsUrl, String subject, String username, String password, String group) {
        this.natsUrl = natsUrl;
        this.subject = subject;
        this.username = username;
        this.password = password;
        this.group = group;
        this.objectMapper = new ObjectMapper();
        this.eventQueue = new ArrayBlockingQueue<>(8888888);
    }
    
    @Override
    public void start() {
        try {
            // 连接NATS
            Options.Builder optionsBuilder = new Options.Builder().server(natsUrl);
            
            // 如果提供了用户名和密码，则添加认证
            if (username != null && password != null) {
                optionsBuilder.userInfo(username, password);
                System.out.println("使用用户名密码连接NATS: " + username);
            }
            
            natsConnection = Nats.connect(optionsBuilder.build());
            
            // 创建Dispatcher
            dispatcher = natsConnection.createDispatcher((message) -> {
                try {
                    String jsonData = new String(message.getData());
                    LogEvent event = parseJsonToLogEvent(jsonData);
                    if (event != null) {
                        eventQueue.offer(event);
                    }
                } catch (Exception e) {
                    System.err.println("解析NATS消息失败: " + e.getMessage());
                }
            });
            
            // 使用Dispatcher订阅主题，如果提供了group则使用队列组订阅
            if (group != null && !group.trim().isEmpty()) {
                dispatcher.subscribe(subject, group);
                System.out.println("NATS连接成功，使用队列组订阅主题: " + subject + ", 组名: " + group);
            } else {
                dispatcher.subscribe(subject);
                System.out.println("NATS连接成功，订阅主题: " + subject);
            }
        } catch (Exception e) {
            throw new RuntimeException("连接NATS失败", e);
        }
    }
    
    @Override
    public InputStatus pollNext(ReaderOutput<LogEvent> output) {
        try {
            LogEvent event = eventQueue.poll(100, TimeUnit.MILLISECONDS);
            if (event != null) {
                output.collect(event);
                return InputStatus.MORE_AVAILABLE;
            }
            return InputStatus.NOTHING_AVAILABLE;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return InputStatus.END_OF_INPUT;
        }
    }
    
    @Override
    public List<NatsLogEventSplit> snapshotState(long checkpointId) {
        return Collections.emptyList();
    }
    
    @Override
    public CompletableFuture<Void> isAvailable() {
        return eventQueue.isEmpty() ? 
            CompletableFuture.completedFuture(null) : 
            CompletableFuture.completedFuture(null);
    }
    
    @Override
    public void addSplits(List<NatsLogEventSplit> splits) {
        // 处理分片分配
    }
    
    @Override
    public void notifyNoMoreSplits() {
        // 通知没有更多分片
    }
    
    @Override
    public void close() throws Exception {
        // 关闭订阅
        if (subscription != null) {
            subscription.unsubscribe();
        }
        
        // 关闭Dispatcher
        if (dispatcher != null) {
            if (group != null && !group.trim().isEmpty()) {
                dispatcher.unsubscribe(subject);
            } else {
                dispatcher.unsubscribe(subject);
            }
        }
        
        // 关闭连接
        if (natsConnection != null) {
            natsConnection.close();
        }
    }
    
    private LogEvent parseJsonToLogEvent(String jsonData) {
        try {
            JsonNode rootNode = objectMapper.readTree(jsonData);
            
            // 提取固定字段
            String logType = rootNode.get("__log_type__").asText();
            String timestampStr = rootNode.get("__timestamp__").asText();
            ZonedDateTime timestamp;
            try {
                // 优先尝试解析ISO_OFFSET_DATE_TIME格式
                OffsetDateTime offsetDateTime = OffsetDateTime.parse(timestampStr, DateTimeFormatter.ISO_OFFSET_DATE_TIME);
                timestamp = offsetDateTime.toZonedDateTime();
            } catch (Exception e1) {
                // 如果解析失败，使用当前时间
                timestamp = ZonedDateTime.now();
                System.err.println("时间戳解析失败，使用当前时间: " + timestampStr + ", 错误: " + e1.getMessage());
                throw e1;
            }
            
            // 提取其他动态字段
            Map<String, Object> fields = new HashMap<>();
            rootNode.fields().forEachRemaining(entry -> {
                String fieldName = entry.getKey();
                if (!"__log_type__".equals(fieldName) && !"__timestamp__".equals(fieldName)) {
                    fields.put(fieldName, entry.getValue().asText());
                }
            });
            
            return new LogEvent(logType, timestamp, fields);
        } catch (Exception e) {
            System.err.println("解析JSON失败: " + e.getMessage());
            return null;
        }
    }
} 