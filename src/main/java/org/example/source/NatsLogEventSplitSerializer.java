package org.example.source;

import org.apache.flink.core.io.SimpleVersionedSerializer;

/**
 * NATS分片序列化器
 */
public class NatsLogEventSplitSerializer implements SimpleVersionedSerializer<NatsLogEventSplit> {
    @Override
    public int getVersion() {
        return 1;
    }
    
    @Override
    public byte[] serialize(NatsLogEventSplit split) {
        return split.getSubject().getBytes();
    }
    
    @Override
    public NatsLogEventSplit deserialize(int version, byte[] serialized) {
        return new NatsLogEventSplit(new String(serialized));
    }
} 