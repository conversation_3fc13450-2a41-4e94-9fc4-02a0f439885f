package org.example;

import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.cep.CEP;
import org.apache.flink.cep.PatternSelectFunction;
import org.apache.flink.cep.PatternStream;
import org.apache.flink.cep.pattern.Pattern;
import org.apache.flink.cep.pattern.conditions.SimpleCondition;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.KeyedStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

import org.apache.flink.util.ParameterTool;
import org.example.model.AlertEvent;
import org.example.model.LogEvent;
import org.example.sink.AlertEventPostgreSQLSink;
import org.example.source.NatsLogEventSource;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 简化CEP作业
 */
public class SimpleCEPJob {

    public static void main(String[] args) throws Exception {
        System.out.println("=== 简化CEP作业启动 ===");
        
        // 获取参数
        ParameterTool params = ParameterTool.fromArgs(args);
        
        // 数据源类型配置
        String sourceType = params.get("source.type", "kafka");
        String dbUrl = params.get("db.url", "***************************************************************************************************************");
        String dbUser = params.get("db.user", "admin");
        String dbPassword = params.get("db.password", "JAY9^yyds#68");
        
        // Kafka 相关配置
        String kafkaBootstrapServers = params.get("kafka.bootstrap.servers", "192.168.188.188:9092");
        String kafkaTopic = params.get("kafka.topic", "log_events");
        String kafkaGroupId = params.get("kafka.group.id", "flink_simple_cep_group");
        String kafkaOffsetReset = params.get("kafka.auto.offset.reset", "latest");
        
        // 设置执行环境
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);
        
        System.out.println("🔧 环境配置:");
        System.out.println("   数据源类型: " + sourceType);
        System.out.println("   并行度: " + env.getParallelism());
        
        // 创建Kafka数据流
        System.out.println("🔌 使用Kafka数据源:");
        System.out.println("   服务器: " + kafkaBootstrapServers);
        System.out.println("   主题: " + kafkaTopic);
        System.out.println("   消费组: " + kafkaGroupId);
        
        OffsetsInitializer offsetsInitializer = "latest".equalsIgnoreCase(kafkaOffsetReset) ? 
                OffsetsInitializer.latest() : OffsetsInitializer.earliest();
        
        KafkaSource<LogEvent> kafkaSource = KafkaSource.<LogEvent>builder()
                .setBootstrapServers(kafkaBootstrapServers)
                .setTopics(kafkaTopic)
                .setGroupId(kafkaGroupId)
                .setStartingOffsets(offsetsInitializer)
                .setValueOnlyDeserializer(new DynamicCEPJob.LogEventDeserializationSchema())
                .build();

        // 首先创建数据流
        DataStream<LogEvent> dataStream = env.fromSource(
                kafkaSource,
                WatermarkStrategy.<LogEvent>noWatermarks(),
                "Kafka Log Source - Processing Time"
        );
        
        // 添加事件接收调试信息
        dataStream = dataStream.map(event -> {
            System.out.println("🔍 接收事件: " + event.logType + 
                ", timestamp=" + event.timestamp + 
                ", user_id=" + event.getFieldValue("user_id") + 
                ", res=" + event.getFieldValue("res"));
            return event;
        });
        
        // 在CEP之前添加调试
        dataStream = dataStream.map(event -> {
            System.out.println("🎪 事件即将进入CEP: " + 
                "类型=" + event.logType + 
                ", user_id=" + event.getFieldValue("user_id") + 
                ", res=" + event.getFieldValue("res"));
            return event;
        });
        
        // 按user_id分组
        String groupKey = "user_id";
        System.out.println("🔑 使用分组键: " + groupKey);
        KeyedStream<LogEvent, String> keyedStream = dataStream.assignTimestampsAndWatermarks(
                WatermarkStrategy
                        .<LogEvent>forBoundedOutOfOrderness(Duration.ofSeconds(5))
                        .withTimestampAssigner((person, ts) -> person.timestamp.toInstant().toEpochMilli())
        ).keyBy(event -> {
            String keyValue = event.getFieldValue(groupKey);
            String finalKey = (keyValue != null) ? keyValue : "unknown";
            System.out.println("   🔑 事件分组: " + finalKey + " -> " + event.getFieldValue("res"));
            return finalKey;
        });
        

        Pattern<LogEvent, ?> pattern = Pattern.<LogEvent>begin("open_event")
            .where(new SimpleCondition<LogEvent>() {
                private int callCount = 0;
                
                @Override
                public boolean filter(LogEvent event) {
                    callCount++;
                    System.out.println("🎯 *** 简单CEP条件检查 *** (第" + callCount + "次调用)");
                    System.out.println("   事件类型: " + event.logType);
                    System.out.println("   user_id: " + event.getFieldValue("user_id"));
                    System.out.println("   res: " + event.getFieldValue("res"));
                    System.out.println("   时间: " + event.timestamp);
                    
                    // 检查事件类型
                    if (!"tunnel_log".equals(event.logType)) {
                        System.out.println("   ❌ 事件类型不匹配: " + event.logType + " != tunnel_log");
                        return false;
                    }
                    
                    // 检查res字段
                    boolean match = "OPEN".equals(event.getFieldValue("res"));
                    System.out.println("   " + (match ? "✅" : "❌") + " res字段匹配结果: " + match);
                    System.out.println("   ============");
                    
                    return match;
                }
            });
        
        System.out.println("✅ CEP Pattern创建完成");
        
        // 应用CEP到KeyedStream
        System.out.println("📡 应用CEP到KeyedStream...");
        PatternStream<LogEvent> patternStream = CEP.pattern(keyedStream, pattern).inEventTime();
        System.out.println("✅ CEP应用完成，开始监听匹配...");
        
        // 处理匹配结果
        DataStream<AlertEvent> alertStream = patternStream.select(
            new PatternSelectFunction<LogEvent, AlertEvent>() {
                @Override
                public AlertEvent select(Map<String, List<LogEvent>> matchedPattern) {
                    System.out.println("🎉 *** CEP模式匹配成功! ***");
                    System.out.println("   匹配的步骤: " + matchedPattern.keySet());
                    
                    List<LogEvent> events = matchedPattern.get("open_event");
                    if (events != null && !events.isEmpty()) {
                        LogEvent event = events.get(0);
                        String userId = event.getFieldValue("user_id");
                        
                        String alertMessage = String.format(
                            "简单CEP匹配成功: 用户 %s 执行了 OPEN 操作，时间: %s", 
                            userId, event.timestamp
                        );
                        
                        System.out.println("   告警消息: " + alertMessage);
                        
                        return new AlertEvent(
                            userId,
                            "SIMPLE_OPEN_DETECTED",
                            alertMessage,
                            LocalDateTime.now(),
                            "simple_cep_rule",
                            matchedPattern
                        );
                    }
                    return null;
                }
            }
        ).filter(alert -> alert != null);
        
        // 输出告警
        alertStream.print("告警");
        
        // 可选：输出到数据库  
        if (dbUrl != null && !dbUrl.trim().isEmpty()) {
            alertStream.sinkTo(new AlertEventPostgreSQLSink(dbUrl, dbUser, dbPassword));
            System.out.println("📤 告警将保存到数据库");
        }
        
        System.out.println("🚀 SimpleCEPJob启动完成，等待事件...");
        System.out.println("   期望看到: 🎯 *** 简单CEP条件检查 *** 消息");
        
        // 执行作业
        env.execute("Simple CEP Job");
    }
} 