package org.example.config;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * CEP规则配置类
 */
public class CEPRuleConfig implements Serializable {
    private static final long serialVersionUID = 1L;
    public String id;
    public String groupKey;
    public List<SubRuleGroup> rules;
    public String expression;
    public WithinConfig within;
    public int status;
    public Timestamp addTime;
    public Timestamp updateTime;
    
    @Override
    public String toString() {
        return String.format("CEPRuleConfig{id='%s', rules=%s, expression='%s', within=%s, status=%d}", 
            id, rules, expression, within, status);
    }
} 