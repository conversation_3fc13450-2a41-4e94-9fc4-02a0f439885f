package org.example.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;

/**
 * 子规则类
 */
public class SubRule implements Serializable {
    private static final long serialVersionUID = 1L;
    public String id;
    public String type;
    @JsonProperty("match_key")
    public String matchKey;
    @JsonProperty("match_type")
    public String matchType;
    @JsonProperty("match_val")
    public String matchVal;
    @JsonProperty("match_times")
    public String matchTimes;
    
    @Override
    public String toString() {
        return String.format("SubRule{id='%s', type='%s', matchKey='%s', matchType='%s', matchVal='%s', matchTimes='%s'}", 
            id, type, matchKey, matchType, matchVal, matchTimes);
    }
} 