package org.example.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;

/**
 * 条件配置类
 */
public class Condition implements Serializable {
    private static final long serialVersionUID = 1L;
    public String id;
    public String type;
    @JsonProperty("match_key")
    public String matchKey;
    @JsonProperty("match_type")
    public String matchType;
    @JsonProperty("match_val")
    public String matchVal;
    
    @Override
    public String toString() {
        return String.format("Condition{id='%s', type='%s', matchKey='%s', matchType='%s', matchVal='%s'}", 
            id, type, matchKey, matchType, matchVal);
    }
} 