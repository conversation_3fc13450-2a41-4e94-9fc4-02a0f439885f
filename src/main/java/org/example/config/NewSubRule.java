package org.example.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.List;

/**
 * 新的子规则类
 */
public class NewSubRule implements Serializable {
    private static final long serialVersionUID = 1L;
    public String id;
    public int[] times;
    public Boolean greedy;
    public Boolean optional;
    @JsonProperty("one_or_more")
    public Boolean oneOrMore;
    public Boolean follow;  // 为true时使用followedBy，否则使用next
    public Boolean not;     // 为true时使用notFollowedBy或notNext，否则使用followedBy或next
    public List<Condition> condition;
    @JsonProperty("condition_expression")
    public String conditionExpression;
    
    @Override
    public String toString() {
        return String.format("NewSubRule{id='%s', times=%s, greedy=%s, optional=%s, oneOrMore=%s, follow=%s, not=%s, condition=%s, conditionExpression='%s'}", 
            id, java.util.Arrays.toString(times), greedy, optional, oneOrMore, follow, not, condition, conditionExpression);
    }
} 