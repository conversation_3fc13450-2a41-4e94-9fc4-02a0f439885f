package org.example.config;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

/**
 * 新的子规则组类
 */
public class NewSubRuleGroup implements Serializable {
    private static final long serialVersionUID = 1L;
    public String id;
    public String[] source;
    public String groupKey;
    public String windowUnit;
    public int windowSize;
    public int windowSlideSize;
    public List<NewSubRule> rules;
    
    @Override
    public String toString() {
        return String.format("NewSubRuleGroup{id='%s', source=%s, groupKey='%s', windowUnit='%s', windowSize=%d, windowSlideSize=%d, rules=%s}", 
            id, Arrays.toString(source), groupKey, windowUnit, windowSize, windowSlideSize, rules);
    }
} 