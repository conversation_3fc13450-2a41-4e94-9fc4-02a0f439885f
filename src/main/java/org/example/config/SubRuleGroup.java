package org.example.config;

import java.io.Serializable;
import java.util.List;

/**
 * 子规则组类
 */
public class SubRuleGroup implements Serializable {
    private static final long serialVersionUID = 1L;
    public String id;
    public String source;
    public String groupKey;
    public String windowUnit;
    public int windowSize;
    public int windowSlideSize;
    public List<SubRule> rules;
    
    @Override
    public String toString() {
        return String.format("SubRuleGroup{id='%s', source='%s', groupKey='%s', rules=%s}", 
            id, source, groupKey, rules);
    }
} 