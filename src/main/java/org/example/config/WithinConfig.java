package org.example.config;

import java.io.Serializable;
import java.time.Duration;

/**
 * 时间窗口配置类
 */
public class WithinConfig implements Serializable {
    private static final long serialVersionUID = 1L;
    public int time;
    public String unit;
    
    public Duration toDuration() {
        switch (unit.toLowerCase()) {
            case "s":
                return Duration.ofSeconds(time);
            case "m":
                return Duration.ofMinutes(time);
            case "h":
                return Duration.ofHours(time);
            default:
                return Duration.ofSeconds(time);
        }
    }
    
    @Override
    public String toString() {
        return String.format("WithinConfig{time=%d, unit='%s'}", time, unit);
    }
} 