package org.example.config;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * 新的CEP规则配置类，对应数据库表结构
 */
public class NewCEPRuleConfig implements Serializable {
    private static final long serialVersionUID = 1L;
    public String id;
    public String groupKey;  // 数据库表的group_key字段
    public List<NewSubRuleGroup> rules;  // JSONB格式的rules字段
    public WithinConfig within;  // 数据库表的within字段
    public int status;
    public Timestamp addTime;
    public Timestamp updateTime;
    
    @Override
    public String toString() {
        return String.format("NewCEPRuleConfig{id='%s', groupKey='%s', rules=%s, within=%s, status=%d}", 
            id, groupKey, rules, within, status);
    }
} 