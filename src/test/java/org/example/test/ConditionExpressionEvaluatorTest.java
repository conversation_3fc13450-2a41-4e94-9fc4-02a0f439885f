package org.example.test;

import org.example.config.Condition;
import org.example.model.LogEvent;
import org.example.processor.ConditionExpressionEvaluator;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 条件表达式解析器测试类
 */
public class ConditionExpressionEvaluatorTest {
    
    public static void main(String[] args) {
        System.out.println("开始测试条件表达式解析器...");
        
        // 创建测试用的条件
        List<Condition> conditions = createTestConditions();
        
        // 创建测试用的日志事件
        LogEvent event = createTestEvent();
        
        // 测试各种表达式
        testExpressions(conditions, event);
        
        System.out.println("测试完成！");
    }
    
    private static List<Condition> createTestConditions() {
        Condition condition1 = new Condition();
        condition1.id = "1";
        condition1.type = "match";
        condition1.matchKey = "status";
        condition1.matchType = "=";
        condition1.matchVal = "SUCCESS";
        
        Condition condition2 = new Condition();
        condition2.id = "2";
        condition2.type = "match";
        condition2.matchKey = "level";
        condition2.matchType = "=";
        condition2.matchVal = "ERROR";
        
        Condition condition3 = new Condition();
        condition3.id = "3";
        condition3.type = "match";
        condition3.matchKey = "user";
        condition3.matchType = "=";
        condition3.matchVal = "admin";
        
        Condition condition4 = new Condition();
        condition4.id = "4";
        condition4.type = "match";
        condition4.matchKey = "amount";
        condition4.matchType = ">";
        condition4.matchVal = "1000";
        
        Condition condition5 = new Condition();
        condition5.id = "5";
        condition5.type = "match";
        condition5.matchKey = "message";
        condition5.matchType = "contains";
        condition5.matchVal = "login";
        
        Condition condition6 = new Condition();
        condition6.id = "6";
        condition6.type = "match";
        condition6.matchKey = "ip";
        condition6.matchType = "startsWith";
        condition6.matchVal = "192.168";
        
        return Arrays.asList(condition1, condition2, condition3, condition4, condition5, condition6);
    }
    
    private static LogEvent createTestEvent() {
        Map<String, Object> fields = new HashMap<>();
        fields.put("status", "SUCCESS");
        fields.put("level", "INFO");
        fields.put("user", "admin");
        fields.put("amount", "1500");
        fields.put("message", "user login successful");
        fields.put("ip", "*************");
        
        LogEvent event = new LogEvent();
        event.logType = "test_log";
        event.timestamp = ZonedDateTime.now();
        event.fields = fields;

        return event;
    }
    
    private static void testExpressions(List<Condition> conditions, LogEvent event) {
        // 测试用例
        String[] testCases = {
            // 简单表达式
            "1",                                    // true (status = SUCCESS)
            "2",                                    // false (level != ERROR)
            "3",                                    // true (user = admin)
            
            // AND 表达式
            "1 AND 3",                             // true (SUCCESS AND admin)
            "1 AND 2",                             // false (SUCCESS AND ERROR)
            
            // OR 表达式
            "1 OR 2",                              // true (SUCCESS OR ERROR)
            "2 OR 4",                              // true (ERROR OR amount>1000)
            
            // NOT 表达式
            "NOT 2",                               // true (NOT ERROR)
            "NOT 1",                               // false (NOT SUCCESS)
            
            // 简单括号
            "(1 AND 3)",                          // true
            "(1 OR 2) AND 3",                     // true
            
            // 复杂嵌套括号
            "(1 AND (2 OR 3))",                   // true: SUCCESS AND (ERROR OR admin)
            "(1 AND (2 OR (5 AND 6)))",           // true: SUCCESS AND (ERROR OR (login AND 192.168))
            "((1 OR 2) AND (3 OR 4))",            // true: (SUCCESS OR ERROR) AND (admin OR amount>1000)
            
            // 更复杂的嵌套
            "(1 AND (2 OR (3 AND (4 OR 5))))",    // true: SUCCESS AND (ERROR OR (admin AND (amount>1000 OR login)))
            "((1 AND 2) OR (3 AND 4)) AND 5",     // true: ((SUCCESS AND ERROR) OR (admin AND amount>1000)) AND login
            
            // NOT 与括号结合
            "NOT (1 AND 2)",                       // true: NOT (SUCCESS AND ERROR)
            "(NOT 1) OR 2",                        // false: (NOT SUCCESS) OR ERROR
            "1 AND (NOT 2)",                       // true: SUCCESS AND (NOT ERROR)
            
            // 操作符优先级测试
            "1 OR 2 AND 3",                        // true: 1 OR (2 AND 3) = SUCCESS OR (ERROR AND admin)
            "2 AND 3 OR 1",                        // true: (2 AND 3) OR 1 = (ERROR AND admin) OR SUCCESS
        };
        
        System.out.println("事件数据:");
        System.out.println("  status = SUCCESS");
        System.out.println("  level = INFO");
        System.out.println("  user = admin");
        System.out.println("  amount = 1500");
        System.out.println("  message = user login successful");
        System.out.println("  ip = *************");
        System.out.println();
        
        System.out.println("条件定义:");
        System.out.println("  1: status = SUCCESS");
        System.out.println("  2: level = ERROR");
        System.out.println("  3: user = admin");
        System.out.println("  4: amount > 1000");
        System.out.println("  5: message contains login");
        System.out.println("  6: ip startsWith 192.168");
        System.out.println();
        
        System.out.println("测试结果:");
        for (String expression : testCases) {
            try {
                boolean result = ConditionExpressionEvaluator.evaluate(expression, conditions, event);
                System.out.printf("%-40s => %s\n", expression, result);
            } catch (Exception e) {
                System.out.printf("%-40s => ERROR: %s\n", expression, e.getMessage());
                e.printStackTrace();
            }
        }
    }
} 