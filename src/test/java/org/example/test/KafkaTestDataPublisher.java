package org.example.test;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.serialization.StringSerializer;

import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.time.ZonedDateTime;
import java.util.Properties;
import java.util.concurrent.TimeUnit;

/**
 * Kafka测试数据发布器
 * 用于生成和发布auth_log和tunnel_log类型的测试数据到Kafka
 * 支持反复隧道连接和认证成功但连接失败的测试场景
 * 使用DynamicCEPJob中的默认Kafka配置信息
 */
public class KafkaTestDataPublisher {

    // 使用DynamicCEPJob中的默认Kafka配置信息
    private static final String KAFKA_BOOTSTRAP_SERVERS = "192.168.188.188:9092";
    private static final String KAFKA_TOPIC = "log_events";
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final DateTimeFormatter formatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;

    private KafkaProducer<String, String> producer;

    public void connect() throws Exception {
        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, KAFKA_BOOTSTRAP_SERVERS);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.ACKS_CONFIG, "all");
        props.put(ProducerConfig.RETRIES_CONFIG, 3);
        props.put(ProducerConfig.LINGER_MS_CONFIG, 1);
        props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 33554432);
        
        producer = new KafkaProducer<>(props);
        System.out.println("Connected to Kafka server: " + KAFKA_BOOTSTRAP_SERVERS);
    }

    public void disconnect() throws Exception {
        if (producer != null) {
            producer.close();
            System.out.println("Disconnected from Kafka server");
        }
    }

    /**
     * 发送认证日志
     */
    public void publishAuthLog(String userId, String result, String timestamp) throws Exception {
        ObjectNode logEvent = objectMapper.createObjectNode();
        logEvent.put("__log_type__", "auth_log");
        logEvent.put("__timestamp__", timestamp);
        logEvent.put("user_id", userId);
        logEvent.put("res", result);

        String jsonMessage = objectMapper.writeValueAsString(logEvent);
        
        // 使用 user_id 作为 key 来确保同一用户的消息发送到同一分区
        ProducerRecord<String, String> record = new ProducerRecord<>(KAFKA_TOPIC, userId, jsonMessage);
        
        producer.send(record, (metadata, exception) -> {
            if (exception != null) {
                System.err.println("Failed to publish auth_log: " + exception.getMessage());
            } else {
                System.out.println("Published auth_log to partition " + metadata.partition() + 
                                 " at offset " + metadata.offset() + ": " + jsonMessage);
            }
        });
    }

    /**
     * 发送隧道日志
     */
    public void publishTunnelLog(String userId, String result, String timestamp) throws Exception {
        ObjectNode logEvent = objectMapper.createObjectNode();
        logEvent.put("__log_type__", "tunnel_log");
        logEvent.put("__timestamp__", timestamp);
        logEvent.put("user_id", userId);
        logEvent.put("res", result);

        String jsonMessage = objectMapper.writeValueAsString(logEvent);
        
        // 使用 user_id 作为 key 来确保同一用户的消息发送到同一分区
        ProducerRecord<String, String> record = new ProducerRecord<>(KAFKA_TOPIC, userId, jsonMessage);
        
        producer.send(record, (metadata, exception) -> {
            if (exception != null) {
                System.err.println("Failed to publish tunnel_log: " + exception.getMessage());
            } else {
                System.out.println("Published tunnel_log to partition " + metadata.partition() + 
                                 " at offset " + metadata.offset() + ": " + jsonMessage);
            }
        });
    }

    /**
     * 获取当前时间戳字符串
     */
    private String getCurrentTimestamp() {
        return OffsetDateTime.now().format(formatter);
    }

    /**
     * 获取指定偏移秒数后的时间戳
     */
    private String getTimestampAfterSeconds(int seconds) {
        return OffsetDateTime.now().plusSeconds(seconds).format(formatter);
    }

    /**
     * 测试场景1: 反复隧道连接
     * 用户在1分钟内反复进行OPEN->CLOSE->OPEN操作
     */
    public void testRepeatedTunnelConnections() throws Exception {
        System.out.println("\n=== 开始测试场景1: 反复隧道连接 ===");
        
        String userId = "test_user_tunnel_repeat";
        String baseTime = getCurrentTimestamp();

        // 第一次打开隧道
        publishTunnelLog(userId, "OPEN", baseTime);
        Thread.sleep(1000); // 等待1秒

        // 关闭隧道
        publishTunnelLog(userId, "CLOSE", getTimestampAfterSeconds(1));
        Thread.sleep(1000); // 等待1秒

        // 再次打开隧道 - 这应该触发告警
        publishTunnelLog(userId, "OPEN", getTimestampAfterSeconds(2));
        Thread.sleep(1000); // 等待1秒

        // 可选：再次关闭和打开
        publishTunnelLog(userId, "CLOSE", getTimestampAfterSeconds(3));
        Thread.sleep(1000);
        publishTunnelLog(userId, "OPEN", getTimestampAfterSeconds(4));
        publishTunnelLog(userId, "CLOSE", getTimestampAfterSeconds(15));

        // 确保消息都发送完成
        producer.flush();
        System.out.println("=== 测试场景1完成 ===\n");
    }

    /**
     * 测试场景1扩展: 多次反复隧道连接（使用不同用户避免重叠匹配）
     */
    public void testMultipleRepeatedTunnelConnections() throws Exception {
        System.out.println("\n=== 开始测试场景1扩展: 多次反复隧道连接 ===");
        
        // 用户1：第一次反复连接
        String userId1 = "test_user_tunnel_repeat_1";
        publishTunnelLog(userId1, "OPEN", getCurrentTimestamp());
        Thread.sleep(500);
        publishTunnelLog(userId1, "CLOSE", getTimestampAfterSeconds(1));
        Thread.sleep(500);
        publishTunnelLog(userId1, "OPEN", getTimestampAfterSeconds(2));
        
        Thread.sleep(1000);
        
        // 用户2：第二次反复连接
        String userId2 = "test_user_tunnel_repeat_2";
        publishTunnelLog(userId2, "OPEN", getCurrentTimestamp());
        Thread.sleep(500);
        publishTunnelLog(userId2, "CLOSE", getTimestampAfterSeconds(1));
        Thread.sleep(500);
        publishTunnelLog(userId2, "OPEN", getTimestampAfterSeconds(2));

        // 确保消息都发送完成
        producer.flush();
        System.out.println("=== 测试场景1扩展完成 ===\n");
    }

    /**
     * 测试场景2: 认证成功但连接失败
     * 用户认证成功后10秒内没有隧道连接
     */
    public void testAuthSuccessNoTunnel() throws Exception {
        System.out.println("\n=== 开始测试场景2: 认证成功但连接失败 ===");
        
        String userId = "test_user_auth_no_tunnel";

        // 认证成功
        publishAuthLog(userId, "AUTH_SUCCESS", getCurrentTimestamp());
        
        // 等待11秒，超过10秒窗口期，不发送隧道打开事件
        System.out.println("等待11秒，模拟未建立隧道连接...");
        Thread.sleep(11000);
        publishTunnelLog(userId + "111", "OPEN", getTimestampAfterSeconds(2));
        
        // 确保消息都发送完成
        producer.flush();
        System.out.println("=== 测试场景2完成 ===\n");
    }

    /**
     * 测试场景3: 正常流程（不应该触发告警）
     * 认证成功后正常建立隧道连接
     */
    public void testNormalFlow() throws Exception {
        System.out.println("\n=== 开始测试场景3: 正常流程 ===");
        
        String userId = "test_user_normal";

        // 认证成功
        publishAuthLog(userId, "AUTH_SUCCESS", getCurrentTimestamp());
        Thread.sleep(1000);

        // 5秒后正常建立隧道连接
        publishTunnelLog(userId, "OPEN", getTimestampAfterSeconds(1));
        Thread.sleep(1000);

        // 正常使用后关闭连接
        publishTunnelLog(userId, "CLOSE", getTimestampAfterSeconds(2));

        // 确保消息都发送完成
        producer.flush();
        System.out.println("=== 测试场景3完成 ===\n");
    }

    /**
     * 测试场景4: 认证失败场景
     */
    public void testAuthFailure() throws Exception {
        System.out.println("\n=== 开始测试场景4: 认证失败 ===");
        
        String userId = "test_user_auth_fail";

        // 认证失败
        publishAuthLog(userId, "AUTH_FAIL", getCurrentTimestamp());
        Thread.sleep(1000);

        // 再次尝试认证
        publishAuthLog(userId, "AUTH_FAIL", getTimestampAfterSeconds(1));

        // 确保消息都发送完成
        producer.flush();
        System.out.println("=== 测试场景4完成 ===\n");
    }

    /**
     * 批量测试场景：模拟多用户并发操作
     */
    public void testConcurrentUsers() throws Exception {
        System.out.println("\n=== 开始批量测试: 多用户并发操作 ===");
        
        // 模拟10个用户的各种操作
        for (int i = 1; i <= 10; i++) {
            String userId = "concurrent_user_" + i;
            
            if (i % 3 == 1) {
                // 1/3用户进行反复隧道连接
                publishTunnelLog(userId, "OPEN", getCurrentTimestamp());
                Thread.sleep(200);
                publishTunnelLog(userId, "CLOSE", getTimestampAfterSeconds(1));
                Thread.sleep(200);
                publishTunnelLog(userId, "OPEN", getTimestampAfterSeconds(2));
            } else if (i % 3 == 2) {
                // 1/3用户认证成功但不建立连接
                publishAuthLog(userId, "AUTH_SUCCESS", getCurrentTimestamp());
            } else {
                // 1/3用户正常流程
                publishAuthLog(userId, "AUTH_SUCCESS", getCurrentTimestamp());
                Thread.sleep(200);
                publishTunnelLog(userId, "OPEN", getTimestampAfterSeconds(1));
            }
            
            Thread.sleep(500); // 用户间隔500ms
        }

        // 确保消息都发送完成
        producer.flush();
        System.out.println("=== 批量测试完成 ===\n");
    }

    /**
     * 运行所有测试场景
     */
    public void runAllTests() throws Exception {
        connect();
        
        try {
            // 等待系统准备
            System.out.println("等待3秒，确保CEP系统已启动...");
            Thread.sleep(3000);

            // 运行各种测试场景
            testRepeatedTunnelConnections();
            /*
            Thread.sleep(2000);*/
            //testAuthSuccessNoTunnel();
            // 取消注释以运行更多测试
            /*

            testRepeatedTunnelConnections();
            Thread.sleep(2000);

            testNormalFlow();
            Thread.sleep(2000);

            testAuthFailure();
            Thread.sleep(2000);

            testConcurrentUsers();
            */
            
            // 等待处理完成
            System.out.println("等待5秒，让CEP系统处理完所有事件...");
            Thread.sleep(5000);
            
        } finally {
            disconnect();
        }
    }

    public static void main(String[] args) {
        System.out.println("Kafka测试数据发布器启动时间: " + ZonedDateTime.now().format(DateTimeFormatter.ISO_OFFSET_DATE_TIME));
        System.out.println("Kafka配置信息:");
        System.out.println("  Bootstrap Servers: " + KAFKA_BOOTSTRAP_SERVERS);
        System.out.println("  Topic: " + KAFKA_TOPIC);

        KafkaTestDataPublisher publisher = new KafkaTestDataPublisher();
        
        try {
            if (args.length > 0) {
                String testType = args[0].toLowerCase();
                publisher.connect();
                
                switch (testType) {
                    case "tunnel":
                        publisher.testRepeatedTunnelConnections();
                        break;
                    case "auth":
                        publisher.testAuthSuccessNoTunnel();
                        break;
                    case "normal":
                        publisher.testNormalFlow();
                        break;
                    case "fail":
                        publisher.testAuthFailure();
                        break;
                    case "concurrent":
                        publisher.testConcurrentUsers();
                        break;
                    case "all":
                        publisher.runAllTests();
                        return; // runAllTests已经包含了connect和disconnect
                    default:
                        System.out.println("未知测试类型: " + testType);
                        System.out.println("支持的测试类型: tunnel, auth, normal, fail, concurrent, all");
                        return;
                }
                
                publisher.disconnect();
            } else {
                // 默认运行所有测试
                publisher.runAllTests();
            }
            
            System.out.println("Kafka测试数据发布完成！");
            
        } catch (Exception e) {
            System.err.println("测试执行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
} 