package org.example.test;

import io.nats.client.Connection;
import io.nats.client.Nats;
import io.nats.client.Options;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * NATS测试数据发布器
 * 用于发送模拟的日志事件数据到NATS服务器
 */
public class NatsTestDataPublisher {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
    private static final Random random = new Random();
    
    // NATS连接配置
    private static final String NATS_URL = "nats://***************:4242";
    private static final String NATS_SUBJECT = "log_events";
    private static final String NATS_USERNAME = "dialer_abc";
    private static final String NATS_PASSWORD = "efgh1234";
    
    // 测试用户和IP地址池
    private static final String[] USERS = {"user001", "user002", "user003", "admin", "guest"};
    private static final String[] IPS = {"*************", "*************", "*************", "*********", "************"};
    private static final String[] LOG_TYPES = {"auth_log", "connection_log", "system_log"};
    
    public static void main(String[] args) {
        System.out.println("🚀 启动NATS测试数据发布器...");
        
        try {
            // 创建NATS连接
            Options options = new Options.Builder()
                .server(NATS_URL)
                .userInfo(NATS_USERNAME, NATS_PASSWORD)
                .build();
            
            Connection natsConnection = Nats.connect(options);
            System.out.println("✅ 连接NATS服务器成功: " + NATS_URL);
            
            // 解析命令行参数
            int messageCount = args.length > 0 ? Integer.parseInt(args[0]) : 100;
            int intervalMs = args.length > 1 ? Integer.parseInt(args[1]) : 1000;
            String scenario = args.length > 2 ? args[2] : "mixed";
            
            System.out.println("📊 测试参数:");
            System.out.println("   - 消息数量: " + messageCount);
            System.out.println("   - 发送间隔: " + intervalMs + "ms");
            System.out.println("   - 测试场景: " + scenario);
            System.out.println();
            
            // 根据场景发送数据
            switch (scenario.toLowerCase()) {
                case "auth_failed":
                    sendAuthFailedScenario(natsConnection, messageCount, intervalMs);
                    break;
                case "connection_failed":
                    sendConnectionFailedScenario(natsConnection, messageCount, intervalMs);
                    break;
                case "mixed":
                    sendMixedScenario(natsConnection, messageCount, intervalMs);
                    break;
                case "rule_trigger":
                    sendRuleTriggerScenario(natsConnection, messageCount, intervalMs);
                    break;
                default:
                    sendMixedScenario(natsConnection, messageCount, intervalMs);
            }
            
            natsConnection.close();
            System.out.println("✅ 测试数据发送完成，连接已关闭");
            
        } catch (Exception e) {
            System.err.println("❌ 错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 认证失败场景：模拟用户多次认证失败
     */
    private static void sendAuthFailedScenario(Connection connection, int count, int intervalMs) throws Exception {
        System.out.println("🔐 发送认证失败场景数据...");
        
        String user = USERS[0]; // 固定用户
        String ip = IPS[0];     // 固定IP
        
        for (int i = 0; i < count; i++) {
            ObjectNode logEvent = createLogEvent("auth_log", user, ip);
            logEvent.put("action", "login");
            logEvent.put("result", "failed");
            logEvent.put("reason", "invalid_password");
            logEvent.put("attempt", i + 1);
            
            String message = objectMapper.writeValueAsString(logEvent);
            connection.publish(NATS_SUBJECT, message.getBytes());
            
            System.out.println("📤 [" + (i + 1) + "/" + count + "] " + message);
            
            if (i < count - 1) {
                Thread.sleep(intervalMs);
            }
        }
    }
    
    /**
     * 连接失败场景：模拟连接问题
     */
    private static void sendConnectionFailedScenario(Connection connection, int count, int intervalMs) throws Exception {
        System.out.println("🔌 发送连接失败场景数据...");
        
        String user = USERS[1]; // 固定用户
        String ip = IPS[1];     // 固定IP
        
        for (int i = 0; i < count; i++) {
            ObjectNode logEvent = createLogEvent("connection_log", user, ip);
            logEvent.put("action", "connect");
            logEvent.put("result", "failed");
            logEvent.put("error_code", "CONNECTION_TIMEOUT");
            logEvent.put("retry_count", i + 1);
            
            String message = objectMapper.writeValueAsString(logEvent);
            connection.publish(NATS_SUBJECT, message.getBytes());
            
            System.out.println("📤 [" + (i + 1) + "/" + count + "] " + message);
            
            if (i < count - 1) {
                Thread.sleep(intervalMs);
            }
        }
    }
    
    /**
     * 混合场景：发送各种类型的日志
     */
    private static void sendMixedScenario(Connection connection, int count, int intervalMs) throws Exception {
        System.out.println("🎲 发送混合场景数据...");
        
        for (int i = 0; i < count; i++) {
            String user = USERS[random.nextInt(USERS.length)];
            String ip = IPS[random.nextInt(IPS.length)];
            String logType = LOG_TYPES[random.nextInt(LOG_TYPES.length)];
            
            ObjectNode logEvent = createLogEvent(logType, user, ip);
            
            // 根据日志类型添加不同的字段
            switch (logType) {
                case "auth_log":
                    logEvent.put("action", random.nextBoolean() ? "login" : "logout");
                    logEvent.put("result", random.nextInt(10) < 3 ? "failed" : "success");
                    break;
                case "connection_log":
                    logEvent.put("action", "connect");
                    logEvent.put("result", random.nextInt(10) < 2 ? "failed" : "success");
                    logEvent.put("protocol", random.nextBoolean() ? "TCP" : "UDP");
                    break;
                case "system_log":
                    logEvent.put("level", random.nextInt(10) < 1 ? "ERROR" : "INFO");
                    logEvent.put("module", "system");
                    break;
            }
            
            String message = objectMapper.writeValueAsString(logEvent);
            connection.publish(NATS_SUBJECT, message.getBytes());
            
            System.out.println("📤 [" + (i + 1) + "/" + count + "] " + message);
            
            if (i < count - 1) {
                Thread.sleep(intervalMs);
            }
        }
    }
    
    /**
     * 规则触发场景：发送会触发CEP规则的数据
     */
    private static void sendRuleTriggerScenario(Connection connection, int count, int intervalMs) throws Exception {
        System.out.println("🎯 发送规则触发场景数据...");
        
        String user = "test_user";
        String ip = "*************";
        
        // 发送一系列会触发规则的事件
        for (int i = 0; i < count; i++) {
            ObjectNode logEvent = createLogEvent("auth_log", user, ip);
            
            // 前几次失败，后面成功
            if (i < count * 0.7) {
                logEvent.put("action", "login");
                logEvent.put("result", "failed");
                logEvent.put("reason", "invalid_password");
            } else {
                logEvent.put("action", "login");
                logEvent.put("result", "success");
            }
            
            logEvent.put("session_id", "session_" + System.currentTimeMillis());
            logEvent.put("user_agent", "TestClient/1.0");
            
            String message = objectMapper.writeValueAsString(logEvent);
            connection.publish(NATS_SUBJECT, message.getBytes());
            
            System.out.println("📤 [" + (i + 1) + "/" + count + "] " + message);
            
            if (i < count - 1) {
                Thread.sleep(intervalMs);
            }
        }
    }
    
    /**
     * 创建基础日志事件
     */
    private static ObjectNode createLogEvent(String logType, String user, String ip) {
        ObjectNode logEvent = objectMapper.createObjectNode();
        logEvent.put("__log_type__", logType);
        logEvent.put("__timestamp__", LocalDateTime.now().format(formatter));
        logEvent.put("user_id", user);
        logEvent.put("ip_address", ip);
        logEvent.put("hostname", "test-server-" + random.nextInt(5));
        logEvent.put("source", "test-publisher");
        return logEvent;
    }
    
    /**
     * 打印使用说明
     */
    public static void printUsage() {
        System.out.println("使用方法:");
        System.out.println("java NatsTestDataPublisher [消息数量] [间隔毫秒] [场景类型]");
        System.out.println();
        System.out.println("参数说明:");
        System.out.println("  消息数量: 要发送的消息数量 (默认: 100)");
        System.out.println("  间隔毫秒: 消息发送间隔 (默认: 1000ms)");
        System.out.println("  场景类型: 测试场景 (默认: mixed)");
        System.out.println();
        System.out.println("支持的场景类型:");
        System.out.println("  - auth_failed: 认证失败场景");
        System.out.println("  - connection_failed: 连接失败场景");
        System.out.println("  - mixed: 混合场景");
        System.out.println("  - rule_trigger: 规则触发场景");
        System.out.println();
        System.out.println("示例:");
        System.out.println("  java NatsTestDataPublisher 50 500 auth_failed");
        System.out.println("  java NatsTestDataPublisher 200 100 mixed");
    }
} 