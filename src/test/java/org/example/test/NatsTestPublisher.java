package org.example.test;

import io.nats.client.Connection;
import io.nats.client.Nats;
import io.nats.client.Options;

/**
 * 简单的NATS测试消息发布器
 */
public class NatsTestPublisher {
    
    private static final String NATS_URL = "nats://192.168.188.188:4242";
    private static final String NATS_SUBJECT = "log_events";
    private static final String NATS_USERNAME = "dialer_abc";
    private static final String NATS_PASSWORD = "efgh1234";
    
    public static void main(String[] args) throws Exception {
        // 连接NATS
        Options options = new Options.Builder()
            .server(NATS_URL)
            .userInfo(NATS_USERNAME, NATS_PASSWORD)
            .build();
        
        Connection natsConnection = Nats.connect(options);
        System.out.println("连接NATS成功: " + NATS_URL);
        
        // 发送测试消息
        String[] testMessages = {
            "{\"__log_type__\":\"auth_log\",\"__timestamp__\":\"2024-01-01T10:00:01\",\"user_id\":\"test_user\",\"action\":\"login\",\"result\":\"failed\"}",
            "{\"__log_type__\":\"auth_log\",\"__timestamp__\":\"2024-01-01T10:00:02\",\"user_id\":\"test_user\",\"action\":\"login\",\"result\":\"failed\"}",
            "{\"__log_type__\":\"auth_log\",\"__timestamp__\":\"2024-01-01T10:00:03\",\"user_id\":\"test_user\",\"action\":\"login\",\"result\":\"failed\"}",
            "{\"__log_type__\":\"connection_log\",\"__timestamp__\":\"2024-01-01T10:00:04\",\"user_id\":\"test_user\",\"action\":\"connect\",\"result\":\"success\"}",
            "{\"__log_type__\":\"auth_log\",\"__timestamp__\":\"2024-01-01T10:00:05\",\"user_id\":\"test_user\",\"action\":\"login\",\"result\":\"success\"}"
        };
        
        for (int i = 0; i < testMessages.length; i++) {
            natsConnection.publish(NATS_SUBJECT, testMessages[i].getBytes());
            System.out.println("发送消息 " + (i + 1) + ": " + testMessages[i]);
            Thread.sleep(1000); // 间隔1秒
        }
        
        natsConnection.close();
        System.out.println("测试完成");
    }
} 