# Flink CEP 日志分析作业说明

## 项目概述

基于 `org.example.MemDataStreamJob` 创建了一个新的 Flink CEP（复杂事件处理）作业 `LogAnalysisCEPJob`，用于分析认证和连接日志，检测网络问题。

## 业务场景

系统完整流程：**先认证（AA，认证日志），再建立连接（tunnel，连接日志）**，两者都成功，业务才算成功。

## 检测的问题类型

### 1. 网络不通（NETWORK_UNAVAILABLE）
- **条件**: 有连接失败日志，且10秒内有认证成功的日志
- **含义**: 用户认证成功，但无法建立连接，说明网络不通
- **CEP模式**: 认证成功 → 连接失败（10秒内）

### 2. 网络质量差（POOR_NETWORK_QUALITY）
- **条件**: 反复出现连接失败、连接成功的日志，且之间没有认证日志
- **含义**: 连接不稳定，反复断开重连，网络质量差
- **CEP模式**: 连接失败 → 连接成功 → 连接失败（30秒内）

## 类结构

### 主要类

#### `LogAnalysisCEPJob`
- 主作业类，包含 CEP 模式定义和事件处理逻辑

#### `LogEvent`（内部类）
- **字段**:
  - `userId`: 用户ID
  - `logType`: 日志类型（LogType 枚举）
  - `timestamp`: 时间戳

#### `LogType`（枚举）
- `AUTH_SUCCESS`: 认证成功
- `CONNECTION_SUCCESS`: 连接成功  
- `CONNECTION_FAILED`: 连接失败

#### `AlertEvent`（内部类）
- **字段**:
  - `userId`: 用户ID
  - `alertType`: 告警类型（AlertType 枚举）
  - `message`: 告警消息
  - `timestamp`: 告警时间戳

#### `AlertType`（枚举）
- `NETWORK_UNAVAILABLE`: 网络不通
- `POOR_NETWORK_QUALITY`: 网络质量差

## CEP 模式实现
文档：https://nightlies.apache.org/flink/flink-docs-release-2.0/zh/docs/libs/cep/
### 模式1: 网络不通检测
```java
Pattern<LogEvent, ?> networkUnavailablePattern = Pattern.<LogEvent>begin("authSuccess")
    .where(event -> event.logType == LogType.AUTH_SUCCESS)
    .followedBy("connectionFailed")
    .where(event -> event.logType == LogType.CONNECTION_FAILED)
    .within(Time.seconds(10));
```

### 模式2: 网络质量差检测
```java
Pattern<LogEvent, ?> poorNetworkQualityPattern = Pattern.<LogEvent>begin("firstConnectionFailed")
    .where(event -> event.logType == LogType.CONNECTION_FAILED)
    .followedBy("connectionSuccess")
    .where(event -> event.logType == LogType.CONNECTION_SUCCESS)
    .followedBy("secondConnectionFailed")
    .where(event -> event.logType == LogType.CONNECTION_FAILED)
    .within(Time.seconds(30));
```

## 数据源配置

### Socket数据源
- **认证日志Socket**: `localhost:9999`
- **连接日志Socket**: `localhost:9998`

### 数据格式

#### 认证日志格式 (端口9999)
```
userId,AUTH_SUCCESS,timestamp
```
示例：
```
user1,AUTH_SUCCESS,2024-01-01T12:00:00
user3,AUTH_SUCCESS,2024-01-01T12:00:20
user3,AUTH_SUCCESS,2024-01-01T12:00:21
```

#### 连接日志格式 (端口9998)
```
userId,CONNECTION_SUCCESS/CONNECTION_FAILED,timestamp
```
示例：
```
user1,CONNECTION_FAILED,2024-01-01T12:00:05
user2,CONNECTION_FAILED,2024-01-01T12:00:10
user2,CONNECTION_SUCCESS,2024-01-01T12:00:12
user3,CONNECTION_SUCCESS,2024-01-01T12:00:22
```

> **注意**: timestamp字段是可选的，如果不提供会使用当前时间

## 运行方式

### 1. 启动Socket服务器
在运行Flink作业之前，需要先启动两个Socket服务器来模拟数据源：

#### 启动认证日志Socket (端口9999)
```bash
# Linux/Mac
nc -l 9999

# Windows (使用telnet或netcat工具)
nc -l -p 9999
```

#### 启动连接日志Socket (端口9998)
```bash
# Linux/Mac  
nc -l 9998

# Windows
nc -l -p 9998
```

### 2. 编译项目
```bash
mvn clean compile
```

### 3. 运行Flink作业
在 IDE 中直接运行 `LogAnalysisCEPJob.main()` 方法。
```bash
mvn exec:java -Dexec.mainClass="org.example.LogAnalysisCEPJob"
```

### 4. 发送测试数据
在对应的Socket终端中输入测试数据：

#### 认证日志终端 (9999端口)：
```
user1,AUTH_SUCCESS,2024-01-01T12:00:00
user3,AUTH_SUCCESS,2024-01-01T12:00:20
```

#### 连接日志终端 (9998端口)：
```
user1,CONNECTION_FAILED,2024-01-01T12:00:05
user2,CONNECTION_FAILED,2024-01-01T12:00:10
user2,CONNECTION_SUCCESS,2024-01-01T12:00:12
user2,CONNECTION_FAILED,2024-01-01T12:00:15
user2,CONNECTION_SUCCESS,2024-01-01T12:00:17
user3,CONNECTION_SUCCESS,2024-01-01T12:00:22
```


## 测试demo
### 网络状况差
#### 连接日志终端 (9998端口)：
```
user1,CONNECTION_SUCCESS,2024-01-01T12:00:05
user2,CONNECTION_SUCCESS,2024-01-01T12:00:05
user1,CONNECTION_FAILED,2024-01-01T12:00:06
user3,CONNECTION_FAILED,2024-01-01T12:00:06
user1,CONNECTION_SUCCESS,2024-01-01T12:00:07
user5,CONNECTION_SUCCESS,2024-01-01T12:00:07
user1,CONNECTION_FAILED,2024-01-01T12:00:08
user6,CONNECTION_FAILED,2024-01-01T12:00:08
user1,CONNECTION_SUCCESS,2024-01-01T12:00:09
user6,CONNECTION_SUCCESS,2024-01-01T12:00:15
```

### gateway网络不通
#### 认证日志终端 (9999端口)：
```
user88,AUTH_SUCCESS,2024-01-01T13:00:00
user3,AUTH_SUCCESS,2024-01-01T13:00:02
user3,AUTH_SUCCESS,2024-01-01T13:00:22
```

#### 连接日志终端 (9998端口)：
```
user88,CONNECTION_FAILED,2024-01-01T13:00:02
user2,CONNECTION_FAILED,2024-01-01T13:00:03
user2,CONNECTION_SUCCESS,2024-01-01T13:00:04
user2,CONNECTION_FAILED,2024-01-01T13:00:05
user2,CONNECTION_SUCCESS,2024-01-01T13:00:06
user3,CONNECTION_SUCCESS,2024-01-01T13:00:22
```


## 文档
watermark：https://developer.aliyun.com/article/1152525
窗口：https://zhuanlan.zhihu.com/p/102325190

